package com.yhl.scp.das.controller;

import java.io.*;
import java.net.URLDecoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.ListUtil;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.FileZipUtils;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.google.common.collect.Lists;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.scp.das.core.ActuatorStrategy;
import com.yhl.scp.das.core.AlgorithmResult;
import com.yhl.scp.das.core.IActuator;
import com.yhl.scp.das.core.InputBase;
import com.yhl.scp.das.service.AlgorithmService;
import com.yhl.scp.das.service.FileService;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <code>AlgorithmController</code>
 * <p>
 * AlgorithmController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 13:41:06
 */
@RestController
@RequestMapping("/algorithm")
@Slf4j
public class AlgorithmController extends BaseController {

    @Autowired
    private ActuatorStrategy actuatorStrategy;

    @Autowired
    private AlgorithmService algorithmService;

    @Value("${mps2.schedule.workspace}")
    private String workspace;

    @Value("${dfp.schedule.workspace}")
    private String dfpWorkspace;

    @Resource
    private IpsFeign ipsFeign;

    /**
     * 统一算法入口
     */
    @RequestMapping(value = "create", method = RequestMethod.POST)
    public BaseResponse create(@RequestBody InputBase inputBase) {
        try {
            String actuator = inputBase.getModuleCode();
            if (actuator.contains("AMS")) {
                actuator = "AMS";
            }
            log.info("调用策略actuator：{}", actuator);
            IActuator actuatorSolve = actuatorStrategy.getActuator(actuator);
            if (null == actuatorSolve) {
                return BaseResponse.error("未找到对应算法模块：" + actuator);
            }
            AlgorithmResult result = actuatorSolve.create(inputBase);
            return BaseResponse.success(result.getData());
        } catch (Exception e) {
            log.error("创建算法调用程序异常 e:", e);
            return BaseResponse.error("创建算法调用失败：" + e.getMessage());
        }
    }


    @RequestMapping(value = "check", method = RequestMethod.POST)
    public BaseResponse check(
            @RequestParam(value = "executionNumber") String executionNumber,
            @RequestParam(value = "moduleCode") String moduleCode,
            @RequestParam(value = "algorithmVersion", required = false) String algorithmVersion
    ) {
        log.info("查询算法执行状态,executionNumber:{}", executionNumber);
        try {
            log.info("moduleCode：" + URLDecoder.decode(moduleCode, "UTF-8") + ";executionNumber:" + executionNumber);
            String actuator = StringUtils.isNotBlank(algorithmVersion) ?
                    URLDecoder.decode(moduleCode, "UTF-8") + algorithmVersion : URLDecoder.decode(moduleCode, "UTF-8");
            log.info("actuator：{}", actuator);
            String status = actuatorStrategy.getActuator(actuator).check(executionNumber);
            return BaseResponse.success("", status);
        } catch (Exception e) {
            log.error("算法执行状态程序异常 e：", e);
            return BaseResponse.error("FAIL", "查询算法执行状态失败：" + e.getMessage());
        }
    }

    @RequestMapping(value = "result", method = RequestMethod.POST)
    public BaseResponse result(
            @RequestParam(value = "executionNumber") String executionNumber,
            @RequestParam(value = "moduleCode") String moduleCode,
            @RequestParam(value = "algorithmVersion", required = false) String algorithmVersion
    ) throws UnsupportedEncodingException {
        log.info("读取算法结果,executionNumber:{}，模块：{}", executionNumber, moduleCode);
        String actuator = URLDecoder.decode(moduleCode, "UTF-8");
        AlgorithmResult result = actuatorStrategy.getActuator(actuator).getResult(executionNumber);
        return new BaseResponse(true, result.getData());
    }

    public static final List<String> BASE_DIRECTORIES = Lists.newArrayList("/usr/local/dfp/workspace",
            "/usr/local/mps/workspace", "/usr/local/ams/workspace", "/usr/local/aps/workspace");

    @GetMapping("downloadZip")
    @ApiOperation(value = "下载文件")
    public ResponseEntity<Object> downloadZip(@RequestParam("logId") String logId) {
        AlgorithmLog algorithmLog = ipsFeign.selectAlgorithmLogById(logId).getData();
        if (Objects.isNull(algorithmLog)) {
            throw new BusinessException("算法日志信息无效：" + logId);
        }
        String path = algorithmLog.getFilePath();
        List<String> fileTypes = ListUtil.of("txt", "log", "xlsx", "csv", "json", "xls", "out");
        return FileZipUtils.downloadZip(path, logId, BASE_DIRECTORIES, fileTypes);
    }

    @GetMapping(value = "stopAlgorithm/{id}")
    public BaseResponse<String> doStopAlgorithm(@PathVariable(name = "id") String id) {
        try {
            return algorithmService.doStopAlgorithm(id);
        } catch (Exception e) {
            log.error("中止算法运行程序异常 e：", e);
            return BaseResponse.error("FAIL", "中止算法运行失败：" + e.getMessage());
        }
    }

    @PostMapping(value = "createTxtFile")
    public void createTxtFile(@RequestBody List<String> dataList,
                              @RequestParam("filePath") String filePath,
                              @RequestParam("fileName") String fileName) {
        // 检查文件名是否以 .txt 结尾，如果不是则添加
        if (!fileName.toLowerCase().endsWith(".txt")) {
            fileName = fileName + ".txt";
        }

        // 检查路径是否存在，如果不存在则创建
        File directory = new File(filePath);
        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                log.error("无法创建目录{} ", filePath);
                return;
            }
        }

        // 构建文件的完整路径
        File file = new File(directory, fileName);

        try (FileWriter writer = new FileWriter(file)) {
            // 遍历数据列表，逐行写入文件
            for (String data : dataList) {
                writer.write(data);
                writer.write("\n");
            }
            log.info("文件生成成功{} ", file.getAbsolutePath());
        } catch (IOException e) {
            log.error("写入文件时出错{}", e.getMessage());
        }
    }
}