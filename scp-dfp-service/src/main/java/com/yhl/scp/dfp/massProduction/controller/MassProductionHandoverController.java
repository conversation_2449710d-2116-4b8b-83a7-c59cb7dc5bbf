package com.yhl.scp.dfp.massProduction.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.job.ProductionHandoverStatusJob;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDTO;
import com.yhl.scp.dfp.massProduction.service.MassProductionHandoverService;
import com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MassProductionHandoverController</code>
 * <p>
 * 量产移交信息主表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:22:14
 */
@Slf4j
@Api(tags = "量产移交信息主表控制器")
@RestController
@RequestMapping("massProductionHandover")
public class MassProductionHandoverController extends BaseController {

    @Resource
    private MassProductionHandoverService massProductionHandoverService;

    @Resource
    private ProductionHandoverStatusJob productionHandoverStatusJob;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MassProductionHandoverVO>> page() {
        List<MassProductionHandoverVO> massProductionHandoverList = massProductionHandoverService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MassProductionHandoverVO> pageInfo = new PageInfo<>(massProductionHandoverList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<String> create(@RequestBody MassProductionHandoverDTO massProductionHandoverDTO) {
    	massProductionHandoverDTO.setId(UUIDUtil.getUUID());
    	massProductionHandoverService.doCreate(massProductionHandoverDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, massProductionHandoverDTO.getId());
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MassProductionHandoverDTO massProductionHandoverDTO) {
        return massProductionHandoverService.doUpdate(massProductionHandoverDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        massProductionHandoverService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MassProductionHandoverVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, massProductionHandoverService.selectDetail(id));
    }
    
    @ApiOperation(value = "量产移交发起OA审批")
    @GetMapping(value = "doApproval")
    public BaseResponse<MassProductionHandoverVO> doApproval(@RequestParam(value = "id") String id) {
    	massProductionHandoverService.doApproval(id);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    

    @ApiOperation(value = "同步OA流程状态")
    @GetMapping(value = "doUpdateApprovalStatus")
    public BaseResponse<MassProductionHandoverVO> doUpdateApprovalStatus(@RequestParam(value = "id") String id) {
    	massProductionHandoverService.doUpdateApprovalStatus(id);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "量产移交获取状态")
    @PostMapping(value = "syncStatus")
    public BaseResponse<Void> syncStatus(@RequestBody List<String> approvalIds) {
        return massProductionHandoverService.syncStatus(null, approvalIds);
    }

    @ApiOperation(value = "量产移交获取状态")
    @GetMapping(value = "job")
    public BaseResponse<Void> job() {
        productionHandoverStatusJob.productionHandoverStatusJob();
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    
    @ApiOperation(value = "获取乘联品牌车型")
    @GetMapping(value = "getVehicleModelBrand")
    public BaseResponse<String> getVehicleModelBrand(@RequestParam(value = "oemCode") String oemCode,
    		@RequestParam(value = "vehicleModelCode") String vehicleModelCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, 
        		massProductionHandoverService.getVehicleModelBrand(oemCode, vehicleModelCode));
    }

}
