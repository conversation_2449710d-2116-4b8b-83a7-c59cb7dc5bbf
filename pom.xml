<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yhl.scp</groupId>
    <artifactId>bpim</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <modules>
        <module>scp-biz-common</module>
        <module>scp-dfp-api-ext</module>
        <module>scp-dfp-service</module>

        <module>scp-gateway-service</module>

        <module>scp-ips-api</module>
        <module>scp-ips-api-ext</module>
        <module>scp-ips-service</module>

        <module>scp-mds-api-ext</module>
        <module>scp-mds-service</module>

        <module>scp-mps-api-ext</module>
        <module>scp-mps-service</module>

        <module>scp-mrp-api-ext</module>
        <module>scp-mrp-service</module>

        <module>scp-das-api-ext</module>
        <module>scp-das-service</module>
        <module>scp-das-agent</module>

        <module>scp-dcp-api-ext</module>
        <module>scp-dcp-service</module>
        <module>scp-job-server</module>
        <module>scp-job-core</module>
    </modules>
    <properties>
        <scp.foundation.version>1.0.0-rzz-SNAPSHOT</scp.foundation.version>
        <scp.extension.version>1.0.0-rzz-SNAPSHOT</scp.extension.version>
        <scp.project.version>1.0.0</scp.project.version>
        <yhl.platform.version>rzz-1.0.6</yhl.platform.version>
        <io.seata.version>2.0.0</io.seata.version>
        <java.version>1.8</java.version>
        <aps.java.version>2.0.18-SNAPSHOT</aps.java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <tomcat.version>9.0.68</tomcat.version>
        <spring.version>5.3.20</spring.version>
        <spring.security.version>5.6.9</spring.security.version>
        <jackson.version>2.14.2</jackson.version>
        <spring.boot.version>2.6.8</spring.boot.version>
        <spring.cloud.version>2021.0.1</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.1</spring.cloud.alibaba.version>
        <mysql.connector.version>8.0.30</mysql.connector.version>
        <mybatis.version>3.5.9</mybatis.version>
        <mybatis.starter.version>2.2.2</mybatis.starter.version>
        <mybatis.generator.version>1.4.0</mybatis.generator.version>
        <springfox.swagger.version>3.0.0</springfox.swagger.version>
        <pagehelper.version>1.4.3</pagehelper.version>

        <poi.version>4.1.2</poi.version>
        <quartz.version>2.3.2</quartz.version>
        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.5.2.Final</mapstruct.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>

        <maven.compiler.plugin.version>3.8.1</maven.compiler.plugin.version>
        <maven.assembly.plugin.version>3.3.0</maven.assembly.plugin.version>
        <maven.jar.plugin.version>3.1.0</maven.jar.plugin.version>
        <maven.resources.plugin.version>3.1.0</maven.resources.plugin.version>
        <spring.boot.maven.plugin.version>2.2.0.RELEASE</spring.boot.maven.plugin.version>
        <flowable.version>6.7.2</flowable.version>
        <redisson.version>3.17.0</redisson.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yhl.algorithm</groupId>
                <artifactId>rzz-java-service</artifactId>
                <version>${aps.java.version}</version>
            </dependency>
            <!-- scp-extension -->
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ams-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ams-infra-base</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ams-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dfp-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dfp-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mds-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mds-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-infra-base</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mrp-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mrp-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ods-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ods-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sds-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sds-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sop-infra</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sop-model</artifactId>
                <version>${scp.extension.version}</version>
            </dependency>
            <!-- scp-foundation -->
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-common</artifactId>
                <version>1.0.1-rzz-SNAPSHOT</version>
                <!--<version>${scp.foundation.version}</version>-->
            </dependency>

            <!--<dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ams-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ams-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ams-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-cts-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-das-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dfp-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dfp-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dfp-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mds-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mds-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mds-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-sdk</artifactId>
                <version>${scp.foundation.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.yhl.scp</groupId>
                        <artifactId>scp-mps-infra</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mrp-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mrp-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mrp-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ods-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ods-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ods-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sop-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sop-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sop-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>-->

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sds-api</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sds-domain</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-sds-sdk</artifactId>
                <version>${scp.foundation.version}</version>
            </dependency>

            <!-- project pom dependencies -->
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-biz-common</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ips-api</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-ips-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mrp-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mps-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dfp-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-mds-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-das-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yhl.scp</groupId>
                <artifactId>scp-dcp-api-ext</artifactId>
                <version>${scp.project.version}</version>
            </dependency>

            <!-- Spring boot and Spring cloud pom dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Upgrade to fix CVEs(Common Vulnerabilities & Exposures) -->
            <!-- Tomcat embed -->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <!-- Upgrade to fix CVEs(Common Vulnerabilities & Exposures) -->
            <!-- Spring frameworks -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jcl</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-messaging</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-oxm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <!-- Upgrade to fix CVEs(Common Vulnerabilities & Exposures) -->
            <!-- Jackson frameworks -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <!-- Upgrade to fix CVEs(Common Vulnerabilities & Exposures) -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>2.1.1</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>4.1.77.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>4.1.87.Final</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.70</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.33</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>5.7.3</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring.security.version}</version>
            </dependency>

            <!-- Swagger & knife4j - Open API -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox.swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.20</version>
            </dependency>

            <!-- Database connection driver and utilities -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${mybatis.generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <!-- Upgrade to fix CVEs(Common Vulnerabilities & Exposures) -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.21.7</version>
            </dependency>

            <!-- Jasypt encryption utilities -->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>2.1.2</version>
            </dependency>

            <!-- Google utilities -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.1-jre</version>
            </dependency>

            <!-- Alibaba utilities -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Poi utilities -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- org.apache.commons utilities -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.21</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.9.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>3.6.1</version>
            </dependency>

            <!-- Commons-xxx utilities -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.9.0</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.5</version>
            </dependency>

            <!-- Quartz scheduler utilities -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz-jobs</artifactId>
                <version>${quartz.version}</version>
            </dependency>

            <!-- Mapstruct utilities -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>

            <dependency>
                <groupId>net.sf.json-lib</groupId>
                <artifactId>json-lib</artifactId>
                <classifier>jdk15</classifier>
                <version>2.4</version>
            </dependency>

            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>1.6.2</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.22</version>
            </dependency>

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>1.21</version>
            </dependency>
            <!-- SkyWalking -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>9.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.10.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>4.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.4.0</version>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.yhl.platform</groupId>
                <artifactId>platform-common</artifactId>
                <version>${yhl.platform.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>2.6.6</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.springframework.boot</groupId>-->
            <!--                <artifactId>spring-boot-starter-security</artifactId>-->
            <!--                <version>${spring.boot.version}</version>-->
            <!--            </dependency>-->
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <!--            <id>nexus</id>-->
            <!--            <url>http://10.7.77.100:8081/repository/yhl-reposity-group/</url>-->
            <id>fuyao</id>
            <url>https://nexus.fuyaogroup.com/repository/fuyaogroup-dev/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <!--            <id>nexus</id>-->
            <!--            <url>http://10.7.77.100:8081/repository/yhl-reposity-group/</url>-->
            <id>fuyao</id>
            <url>https://nexus.fuyaogroup.com/repository/fuyaogroup-dev/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>fuyao</id>
            <url>https://nexus.fuyaogroup.com/repository/bpim_snapshot/</url>
        </repository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>