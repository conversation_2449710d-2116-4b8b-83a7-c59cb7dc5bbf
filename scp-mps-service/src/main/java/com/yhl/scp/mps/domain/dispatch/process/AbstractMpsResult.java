package com.yhl.scp.mps.domain.dispatch.process;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.utils.UserThreadLocal;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.rule.util.RuleEncodingsUtils;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.algorithm.enums.DemandTypeEnum;
import com.yhl.scp.mps.algorithm.enums.FulfillmentStatusEnum;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.dispatch.mps.input.ProductionPlannedMergeMapping;
import com.yhl.scp.mps.dispatch.output.*;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.mps.order.infrastructure.dao.WorkOrderDeletionDao;
import com.yhl.scp.mps.order.infrastructure.po.WorkOrderDeletionPO;
import com.yhl.scp.mps.plan.infrastructure.po.ProductionIntervalPO;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationVO;
import com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum;
import com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO;
import com.yhl.scp.sds.basic.enums.KitStatusEnum;
import com.yhl.scp.sds.basic.enums.SynchronizeStatusEnum;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.FulfillmentPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.SupplyPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 结果解析
 */
@Slf4j
public abstract class AbstractMpsResult extends AbstractSchedule {
    // 链式BOM
    private final static String CHAIN_BOM = "CHAIN_BOM";
    // 多级BOM
    private final static String TREE_BOM = "TREE_BOM";
    // 子BOM,作为某个成品的输入订单类型
    private final static String CHILD_BOM = "SEMI_BOM";
    // 限制修改交期的制造订单状态
    private final static List<String> limitStatus = Arrays.asList(PlannedStatusEnum.STARTED.getCode(),
            PlannedStatusEnum.FINISHED.getCode());
    @Resource
    private IWorkOrderSync workOrderSync;
    @Resource
    private IAmsSchedule amsSchedule;
    @Resource
    private WorkOrderDeletionDao workOrderDeletionDao;
    @Resource
    private CacheSetService cacheSetService;

    /**
     * 解析优化算法结果
     */
    @Override
    @BusinessMonitorLog(businessCode = "生产计划编制", moduleCode = "MPS", businessFrequency = "DAY")
    public void doAnalysisAlgorithmOutputData(RzzMpsAlgorithmOutput mpsAlgorithmOutput, AlgorithmLog algorithmLog) {
        UserThreadLocal.set(algorithmLog.getCreator());
        // 初始化MPS结果解析上下文
        MpsAnalysisContext mpsAnalysisContext = super.initResultContext(mpsAlgorithmOutput, algorithmLog);
        // 解析前处理删除未计划制造订单,及锁定期外不存在需求的制造订单
        preDeleteWorkOrder(mpsAnalysisContext, mpsAlgorithmOutput);
        // 解析结果
        analysisWorkOrder(mpsAnalysisContext, mpsAlgorithmOutput);
        // 取消计划
        cancelPlan(mpsAnalysisContext);
        // 制造订单大单拆小单
        splitWorkOrder(mpsAnalysisContext, mpsAlgorithmOutput);
        // MPS结果数据库操作
        databaseOperation(mpsAnalysisContext, mpsAlgorithmOutput);
        // 制造订单重新展开
        sync(mpsAlgorithmOutput, mpsAnalysisContext);
        // AMS调用
        amsSchedule.doAmsSchedule(algorithmLog, mpsAnalysisContext);
        // 重新排序制造订单的
        resetWorkOrderSequence(mpsAnalysisContext, mpsAlgorithmOutput);
        // 记录求解步骤日志
        ipsFeign.batchSaveStepLog(mpsAnalysisContext.getAlgorithmStepLogDTOList());
        // 清除线程上下文
        UserThreadLocal.clear();
    }

    /**
     * 制造订单大单拆小单
     *
     * @param mpsAnalysisContext 应用上下文
     * @param mpsAlgorithmOutput MPS结果
     */
    protected abstract void splitWorkOrder(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput);

    /**
     * 制造订单大单拆小单
     *
     * @param mpsAnalysisContext 应用上下文
     * @param mpsAlgorithmOutput MPS结果
     */
    protected abstract void resetWorkOrderSequence(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput);


    private void sync(RzzMpsAlgorithmOutput mpsAlgorithmOutput, MpsAnalysisContext mpsAnalysisContext) {
        Date dateTime = new Date();
        workOrderSync.doSyncOrder(mpsAlgorithmOutput);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("制造订单展开完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
        // 新展开的订单加入待排制造订单排程
        mpsAnalysisContext.getWorkOrderIds().addAll(mpsAlgorithmOutput.getWorkOrderIds());
    }

    private void supplementaryPackagingProcess(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date operationDate = new Date();
        List<WorkOrderPO> workOrderPOS = new ArrayList<>();
        List<RoutingVO> routingVOS = mpsAnalysisContext.getRoutingVOS();
        List<RoutingStepVO> routingStepVOS = mpsAnalysisContext.getRoutingStepVOS();
        List<ProductCandidateResourceTimeVO> productCandidateResourceVOS = newMdsFeign.selectProductCandidateResourceTimeByParams(null, new HashMap<>());
        List<ProductionLeadTimeVO> productionLeadTimeVOS = productionLeadTimeService.selectAll();

        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap = mpsAnalysisContext.getDeliveryPlanMap();
        Map<String, List<RoutingStepVO>> stepMap = StreamUtils.mapListByColumn(routingStepVOS, RoutingStepVO::getRoutingId);
        Map<String, RoutingVO> routingVOMap = StreamUtils.mapByColumn(routingVOS, RoutingVO::getProductCode);
        Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceMap = productCandidateResourceVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        Map<String, List<ProductionLeadTimeVO>> productionLeadTimeMap = productionLeadTimeVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getOperationCode(), p.getLeadTimeType())));
        // 获取要补充的包装产品
        List<String> supplementaryPackagingCode = supplementaryPackagingProductCode(mpsAnalysisContext, deliveryPlanMap, stepMap, routingVOMap);
        if (CollectionUtils.isEmpty(supplementaryPackagingCode)) {
            log.info("没有发货计划需要补充制造订单");
            return;
        }
        List<SafetyStockLevelVO> safetyStockLevelVOS = dfpFeign.selectSafetyStockLevelByProductCodeList(null, supplementaryPackagingCode);
        Map<String, List<SafetyStockLevelVO>> safetyStockLecelMap = safetyStockLevelVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockCode(), p.getProductCode())));
        for (String supplyProductCode : supplementaryPackagingCode) {
            RoutingVO routingVO = routingVOMap.get(supplyProductCode);
            String stockPointCode = routingVO.getStockPointId();
            // 包装工序
            RoutingStepVO routingStepVO = stepMap.get(routingVO.getId()).get(0);
            Integer sequenceNo = routingStepVO.getSequenceNo();
            String mainKey = CharSequenceUtil.join("&", stockPointCode, supplyProductCode, sequenceNo);
            List<DeliveryPlanPublishedVO> packDeliveryList = deliveryPlanMap.get(supplyProductCode);
            List<List<DeliveryPlanPublishedVO>> partition = Lists.partition(packDeliveryList, 7);
            NewStockPointVO newStockPointVO = mpsAnalysisContext.getStockMap().get(stockPointCode);
            // 7天合成一批
            for (List<DeliveryPlanPublishedVO> deliverList : partition) {
                deliverList.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
                int demandQuantity = deliverList.stream().mapToInt(DeliveryPlanPublishedVO::getDemandQuantity).sum();
                // 最早的发货计划计算提前期
                DeliveryPlanPublishedVO deliveryPlanPublishedVO = deliverList.get(0);
                Date dueDate = deliveryPlanPublishedVO.getDemandTime();
                String safetyKey = CharSequenceUtil.join("&", routingVO.getStockPointId(), supplyProductCode);
                // -标准安全库存天数
                if (safetyStockLecelMap.containsKey(safetyKey)) {
                    SafetyStockLevelVO safetyStockLevelVO = safetyStockLecelMap.get(safetyKey).get(0);
                    int minStockDay = safetyStockLevelVO.getStandardStockDay().intValue();
                    dueDate = DateUtil.offsetDay(dueDate, -minStockDay);
                }
                // -该物料的发货数量 X 包装工序的生产节拍
                if (productCandidateResourceMap.containsKey(mainKey)) {
                    ProductCandidateResourceTimeVO productCandidateResourceVO = productCandidateResourceMap.get(mainKey).get(0);
                    BigDecimal multiple = BigDecimalUtils.multiply(BigDecimalUtils.toBigDecimal(demandQuantity), BigDecimalUtils.toBigDecimal(productCandidateResourceVO.getBeat()));
                    dueDate = DateUtil.offsetSecond(dueDate, -multiple.intValue());
                }
                // -包装工序后处理时间
                String key = CharSequenceUtil.join("&", stockPointCode, sequenceNo, ProductionLeadTimeEnum.POST_PRODUCTION_PROCESSING_TIME.getCode());
                dueDate = calculateTime(key, productionLeadTimeMap, dueDate);
                // 组装workOrder
                String demandCategoryType = getDemandCategoryType(deliverList);
                int endingInventoryMinSafeDiff = getEndingInventoryMinSafeDiff(deliverList, mpsAnalysisContext);
                WorkOrderPO workOrderPOInsert = new WorkOrderPO();
                workOrderPOInsert.setId(UUIDUtil.getUUID());
                workOrderPOInsert.setOrderNo(getWorkOrderCode(mpsAnalysisContext.getRuleEncodingsMap()));
                workOrderPOInsert.setQuantity(BigDecimalUtils.toBigDecimal(demandQuantity));
                workOrderPOInsert.setDueDate(dueDate);
                workOrderPOInsert.setProductId(routingVO.getProductId());
                workOrderPOInsert.setProductStockPointId(routingVO.getProductId());
                workOrderPOInsert.setCreateTime(operationDate);
                workOrderPOInsert.setModifyTime(operationDate);
                workOrderPOInsert.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
                workOrderPOInsert.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
                workOrderPOInsert.setKitStatus(KitStatusEnum.UNKIT.getCode());
                workOrderPOInsert.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
                workOrderPOInsert.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                workOrderPOInsert.setCreateTime(operationDate);
                workOrderPOInsert.setModifyTime(operationDate);
                workOrderPOInsert.setEnabled(YesOrNoEnum.YES.getCode());
                workOrderPOInsert.setRoutingId(routingVO.getId());
                workOrderPOInsert.setRemark("supply workOrder");
                workOrderPOInsert.setBomType("packing");
                workOrderPOInsert.setDemandCategory(demandCategoryType);
                workOrderPOInsert.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderPOInsert.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                workOrderPOS.add(workOrderPOInsert);
            }
        }
        // 更新编码规则
        mdsFeign.selectiveUpdateRuleEncodings(mpsAnalysisContext.getRuleEncodingsMap().get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
        log.info("补充包装制造订单数量：{}", workOrderPOS.size());
        if (CollectionUtils.isNotEmpty(workOrderPOS)) {
            List<String> insertWorkOrder = workOrderPOS.stream().map(WorkOrderPO::getId).collect(Collectors.toList());
            mpsAlgorithmOutput.getWorkOrderIds().addAll(insertWorkOrder);
            workOrderDao.insertBatch(workOrderPOS);
        }
    }

    private List<String> supplementaryPackagingProductCode(MpsAnalysisContext mpsAnalysisContext,
                                                           Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap,
                                                           Map<String, List<RoutingStepVO>> stepMap, Map<String, RoutingVO> routingVOMap) {
        List<WorkOrderVO> workOrderVOS = workOrderService.selectAll();
        Map<String, List<WorkOrderVO>> workOrderMap = StreamUtils.mapListByColumn(workOrderVOS, WorkOrderVO::getProductId);
        Map<String, StandardStepVO> standardStepVOMap = mpsAnalysisContext.getStandardStepVOMap();

        String packing = "包装";
        List<String> supplementaryPackagingCode = new ArrayList<>();
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : deliveryPlanMap.entrySet()) {
            String productCode = entry.getKey();
            RoutingVO routingVO = routingVOMap.get(productCode);
            if (routingVO == null) {
                continue;
            }
            String productId = routingVO.getProductId();
            if (workOrderMap.containsKey(productId)) {
                continue;
            }
            String routingVOId = routingVO.getId();
            List<RoutingStepVO> routingSteps = stepMap.get(routingVOId);
            if (routingSteps == null || routingSteps.size() != 1) {
                continue;
            }
            RoutingStepVO routingStepVO = routingSteps.get(0);
            StandardStepVO standardStepVO = standardStepVOMap.get(routingStepVO.getStandardStepId());
            if (standardStepVO == null || !packing.equals(standardStepVO.getStandardStepName())) {
                continue;
            }
            log.info("发货计划物品：{}，不存在制造订单，准备补充", productCode);
            supplementaryPackagingCode.add(productCode);
        }
        log.info("补充只含包装工序的发货计划：{}", supplementaryPackagingCode);
        return supplementaryPackagingCode.stream().distinct().collect(Collectors.toList());
    }


    protected void deleteBatchWorkOrder(MpsAnalysisContext mpsAnalysisContext) {
        List<String> workOrderCancelPlanIdList = mpsAnalysisContext.getWorkOrderCancelPlanIdList();
        log.info("小批合大批删除制造订单数量：{}", workOrderCancelPlanIdList.size());
        if (CollectionUtils.isEmpty(workOrderCancelPlanIdList)) {
            return;
        }
        deletePlanByWorkOrder(workOrderCancelPlanIdList);
    }

    private void handleDeleteWorkOrder(List<String> deleteWorkOrderIds) {
        List<WorkOrderPO> workOrderPOS = workOrderDao.selectByParams(ImmutableMap.of("ids", deleteWorkOrderIds));
        List<WorkOrderDeletionPO> workOrderDeletionPOS = new ArrayList<>();
        String userId = SystemHolder.getUserId();
        Date date = new Date();
        for (WorkOrderPO workOrderPO : workOrderPOS) {
            WorkOrderDeletionPO workOrderDeletionPO = new WorkOrderDeletionPO();
            BeanUtils.copyProperties(workOrderPO, workOrderDeletionPO);
            workOrderDeletionPO.setCreator(userId);
            workOrderDeletionPO.setModifier(userId);
            workOrderDeletionPO.setCreateTime(date);
            workOrderDeletionPO.setModifyTime(date);
            workOrderDeletionPO.setRemark(workOrderPO.getRemark() + "-自动排程删除订单");
            workOrderDeletionPOS.add(workOrderDeletionPO);
        }
        if (CollectionUtils.isNotEmpty(workOrderDeletionPOS)) {
            BasePOUtils.insertBatchFiller(workOrderDeletionPOS);
            workOrderDeletionDao.insertBatchWithPrimaryKey(workOrderDeletionPOS);
        }
        log.info("记录删除制造订单deletion数量：{}", workOrderDeletionPOS.size());
    }

    private void preDeleteWorkOrder(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date dateTime = new Date();
        // 计划单，仅关联了成品信息
        Map<String, MasterPlanRelationVO> masterPlanRelationMap = mpsAnalysisContext.getMasterPlanRelationMap();
        List<WorkOrderPO> workOrderPOList = mpsAnalysisContext.getWorkOrderAll();
        List<WorkOrderPO> workOrderAll = mpsAnalysisContext.getWorkOrderPOS();
        // 子订单对应订单id
        Map<String, List<WorkOrderPO>> parentOrder = workOrderPOList.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getParentId()))
                .collect(Collectors.groupingBy(WorkOrderPO::getParentId));
        // 当前计划员负责的未计划订单
        List<WorkOrderPO> workOrderPOS = workOrderAll.stream().filter(p ->
                PlannedStatusEnum.UNPLAN.getCode().equals(p.getPlanStatus())).collect(Collectors.toList());
        // MPS运行标记不删除的制造订单
        List<String> lockWorkOrderIds = runCacheWorkOrderIds(RedisKeyManageEnum.MPS_ALGORITHM_FIX_WORK_ORDER.getKey(), mpsAnalysisContext.getCreatorId());
        Map<String, Object> deleteMap = new HashMap<>();
        List<String> deleteWorkOrderIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(workOrderPOS)) {
            List<String> notDeleteOrderIds = new ArrayList<>(lockWorkOrderIds);
            log.info("过滤成品未计划关联计划单前数量：{}", workOrderPOS.size());

            // 过滤出与主计划关联的未计划工单
            List<WorkOrderPO> waitingUnPlanSchedule = workOrderPOS.stream()
                    .filter(p -> masterPlanRelationMap.containsKey(p.getOrderNo()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(waitingUnPlanSchedule)) {
                // 获取这些工单的ID
                List<String> parentOrderIds = StreamUtils.columnToList(waitingUnPlanSchedule, WorkOrderPO::getId);

                // 过滤出这些工单的子工单
                List<WorkOrderPO> childWorkOrder = workOrderPOS.stream()
                        .filter(p -> StrUtil.isNotEmpty(p.getParentId()) && parentOrderIds.contains(p.getParentId()))
                        .collect(Collectors.toList());

                // 如果存在子工单，将它们也加入待排程列表
                if (CollectionUtils.isNotEmpty(childWorkOrder)) {
                    waitingUnPlanSchedule.addAll(childWorkOrder);
                }

                // 将待排程的工单ID加入到 mpsAlgorithmOutput 和 notDeleteOrderIds 中
                List<String> waitingScheduleIds = waitingUnPlanSchedule.stream()
                        .map(WorkOrderPO::getId)
                        .collect(Collectors.toList());
                notDeleteOrderIds.addAll(waitingScheduleIds);
                mpsAlgorithmOutput.getWorkOrderIds().addAll(
                        waitingUnPlanSchedule.stream()
                                .filter(p -> !p.getSyncStatus().equals(SynchronizeStatusEnum.SUCCESS_SYNC.getCode()))
                                .map(WorkOrderPO::getId)
                                .collect(Collectors.toList())
                );
                log.info("关联计划单且未计划的制造订单参与排程数量：{}", waitingUnPlanSchedule.size());
            }

            // 过滤出未关联主计划且未计划的工单，并删除它们
            List<WorkOrderPO> deleteWorkOrder = workOrderPOS.stream()
                    .filter(p -> !notDeleteOrderIds.contains(p.getId()))
                    .collect(Collectors.toList());

            // 构建删除数据
            List<WorkOrderPO> deleteWorkOrderAll = new ArrayList<>();
            // 删除未计划父订单级联删除对应半品订单，半品不会存在未计划情况
            for (WorkOrderPO workOrderPO : deleteWorkOrder) {
                // 成品删除对应半品订单
                deleteWorkOrderAll.add(workOrderPO);
                String id = workOrderPO.getId();
                if (parentOrder.containsKey(id)) {
                    log.info("删除父订单，关联删除子订单：{}", id);
                    List<WorkOrderPO> childWorkOrder = parentOrder.get(id);
                    deleteWorkOrderAll.addAll(childWorkOrder);
                }
            }

            log.info("删除未关联计划单未计划制造订单数量：{}", deleteWorkOrderAll.size());
            if (CollectionUtils.isNotEmpty(deleteWorkOrderAll)) {
                List<String> workOrderId = deleteWorkOrderAll.stream()
                        .map(WorkOrderPO::getId)
                        .collect(Collectors.toList());
                deleteWorkOrderIds.addAll(workOrderId);
            }
        }
        deleteMap.put("UNPLAN", deleteWorkOrderIds);
        deleteMap.put("LOCK", lockWorkOrderIds);
        List<String> waitingDeleteNoDemandWorkOrder = runCacheWorkOrderIds(RedisKeyManageEnum.MPS_ALGORITHM_DELETE_WORK_ORDER.getKey(), mpsAnalysisContext.getCreatorId());
        if (CollectionUtils.isNotEmpty(waitingDeleteNoDemandWorkOrder)) {
            deleteMap.put("MPS", waitingDeleteNoDemandWorkOrder);
            log.info("MPS解析结果处理删除锁定期外制造订单数量：{}", waitingDeleteNoDemandWorkOrder.size());
            deleteWorkOrderIds.addAll(waitingDeleteNoDemandWorkOrder);
        }
        if (CollectionUtils.isNotEmpty(deleteWorkOrderIds)) {
            // 记录删除的制造订单id
            handleDeleteWorkOrder(deleteWorkOrderIds);
            // 执行删除制造订单
            deletePlanByWorkOrder(deleteWorkOrderIds);
        }
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("解析前处理删除订单完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
        mpsAnalysisContext.setScheduleInfoMap(deleteMap);
        log.info("MPS解析前处理完成");
    }

    private List<String> runCacheWorkOrderIds(String key, String creatorId) {
        String redisKey = key.replace("{userId}", creatorId);
        List<Object> cacheDeleteWorkOrderDelete = redisUtil.lGet(redisKey);
        List<String> waitingDeleteNoDemandWorkOrder = CollectionUtils.isEmpty(cacheDeleteWorkOrderDelete) ? new ArrayList<>() :
                (List<String>) cacheDeleteWorkOrderDelete.get(0);
        log.info("cache key：{}", redisKey);
        return waitingDeleteNoDemandWorkOrder;
    }

    private void analysisWorkOrder(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        log.info("开始解析MPS结果");
        Date dateTime1 = new Date();
        List<RzzProductionIntervalOutput> productionIntervalOutputDataList = mpsAnalysisContext.getProductionIntervalOutputDataList();
        List<RzzProductionPlannedInIntervalOutput> productionPlannedInIntervalOutputDataList = mpsAnalysisContext.getProductionPlannedInIntervalOutputDataList();
        Map<String, RzzProductionIntervalOutput> intervalOutputMap = mpsAnalysisContext.getIntervalOutputMap();
        Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMpa = mpsAnalysisContext.getInIntervalMap();
        List<RzzProductionPlannedOutput> productionPlannedOutputDataList = mpsAnalysisContext.getProductionPlannedOutputDataList();
        Map<String, RoutingStepVO> routingStepVOMap = mpsAnalysisContext.getRoutingStepVOMap();
        Map<String, StandardStepVO> standardStepVOMap = mpsAnalysisContext.getStandardStepVOMap();

        // 发货计划&库存推移数据
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = mpsAnalysisContext.getDeliveryPlanPublishedVOS();
        // 供应
        List<RzzSupplyOutput> supplyOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getSupplyOutputDataList();
        // 分配关系
        List<RzzFulfillmentOutput> fulfillmentOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getFulfillmentOutputDataList();
        // 需求
        List<RzzDemandOutput> demandOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getDemandOutputDataList();
        // 循环生产批量，已处理过的半品生产批量跳过不处理
        Map<String, String> processIntervalMap = new HashMap<>();
        Date operationDate = new Date();
        Map<String, List<String>> intervalDemandMap = new HashMap<>();
        List<String> formingProcessBatch = new ArrayList<>();
        String interval = "production_interval";
        String formingProcess = StandardStepEnum.FORMING_PROCESS.getCode();
        for (RzzProductionIntervalOutput productionIntervalOutput : productionIntervalOutputDataList) {
            String productionIntervalId = productionIntervalOutput.getProductionIntervalId();
            if (!productionIntervalId.contains(interval)) {
                formingProcessBatch.add(productionIntervalId);
                continue;
            }
            BigDecimal qty = productionIntervalOutput.getQty();
            List<RzzProductionPlannedInIntervalOutput> inInterval = inIntervalMpa.get(productionIntervalId);
            if (null == inInterval) {
                log.warn("批量：{}，为空", productionIntervalId);
                continue;
            }
            if (CollectionUtils.isEmpty(inInterval) && qty.compareTo(BigDecimal.ZERO) == 0) {
                log.warn("批量：{}，没有输出批量关系且合批数量为0", productionIntervalId);
                continue;
            }
            List<String> plannedIds = inInterval.stream().map(RzzProductionPlannedInIntervalOutput::getProductionPlannedId)
                    .collect(Collectors.toList());
            List<RzzProductionPlannedOutput> productionPlannedOutputs = productionPlannedOutputDataList.stream()
                    .filter(p -> plannedIds.contains(p.getSelfProductionPlannedId())).collect(Collectors.toList());
            // 生产批量对应得supply信息
            List<RzzSupplyOutput> plannedSupply = supplyOutputDataList.stream()
                    .filter(s -> plannedIds.contains(s.getSelfPlannedId())).collect(Collectors.toList());
            List<String> selfSupplyIds = plannedSupply.stream()
                    .map(RzzSupplyOutput::getSelfSupplyId).collect(Collectors.toList());
            // 批量对应得分配关系关联demand信息
            List<RzzFulfillmentOutput> plannedSupplyFulfillment = fulfillmentOutputDataList.stream()
                    .filter(f -> selfSupplyIds.contains(f.getSupplyId())).collect(Collectors.toList());
            List<String> demandIds = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandId).distinct().sorted().collect(Collectors.toList());
            // 判断是否成型批次，镀膜则跳过
            List<String> routingStepIds = productionPlannedOutputs.stream()
                    .map(RzzProductionPlannedOutput::getRoutingStepId).distinct().collect(Collectors.toList());
            for (String routingStepId : routingStepIds) {
                RoutingStepVO routingStepVO = routingStepVOMap.get(routingStepId);
                String standardStepId = routingStepVO.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                String standardStepType = standardStepVO.getStandardStepType();
                if (standardStepType.equals(formingProcess)) {
                    // 标记成型批次
                    formingProcessBatch.add(productionIntervalId);
                    break;
                }
            }
            intervalDemandMap.put(productionIntervalId, demandIds);
        }

        // 仅成型工序创建制造订单
        String fixedValue = "True";
        List<RzzProductionIntervalOutput> productionIntervalUpdateDuedateList = new ArrayList<>();
        productionIntervalOutputDataList.removeIf(p -> !formingProcessBatch.contains(p.getProductionIntervalId()));
        String productLine = mpsAnalysisContext.getAlgorithmLog().getProductLine();
        List<String> productLineCodeList = Arrays.asList(productLine.split(","));
        for (RzzProductionIntervalOutput productionIntervalOutput : productionIntervalOutputDataList) {
            String productLineCode = productionIntervalOutput.getStandardResourceId();
            // 锁定期内外批次判断
            String fixed = productionIntervalOutput.getFixed();
            if (fixed.equals(fixedValue)) {
                log.info("批次：{},制造订单设置固定：", productionIntervalOutput.getProductionIntervalId());
                productionIntervalUpdateDuedateList.add(productionIntervalOutput);
                continue;
            }
            if (!productLineCodeList.contains(productLineCode)) {
                log.info("批量：{}，没有排在指定的权限产线上，不处理：{}", productionIntervalOutput.getProductionIntervalId(), productLineCode);
                continue;
            }
            String productionIntervalId = productionIntervalOutput.getProductionIntervalId();
            BigDecimal qty = productionIntervalOutput.getQty();
            if (qty.compareTo(BigDecimal.ZERO) == 0 && !productionIntervalId.contains(interval)) {
                // 如果当前批次批量=0并且不是新的批次，说明量被合到了另一个批次，当前制造订单做取消计划并且删除
                mpsAnalysisContext.getWorkOrderCancelPlanIdList().add(productionIntervalId);
                continue;
            }
            if (processIntervalMap.containsKey(productionIntervalId)) {
                log.info("批次已经被其他关联物料提前处理，不处理：{}", productionIntervalId);
                continue;
            }
            // 标记批次已经处理
            processIntervalMap.put(productionIntervalId, productionIntervalId);

            List<RzzProductionPlannedInIntervalOutput> inInterval = inIntervalMpa.get(productionIntervalId);
            if (CollectionUtils.isEmpty(inInterval)) {
                log.warn("生产批量关联不到批量明细：{}", productionIntervalId);
                continue;
            }
            List<String> plannedIds = inInterval.stream().map(RzzProductionPlannedInIntervalOutput::getProductionPlannedId)
                    .collect(Collectors.toList());
            // 生产批量对应得supply信息
            List<RzzSupplyOutput> plannedSupply = supplyOutputDataList.stream()
                    .filter(s -> plannedIds.contains(s.getSelfPlannedId())).collect(Collectors.toList());
            List<String> selfSupplyIds = plannedSupply.stream()
                    .map(RzzSupplyOutput::getSelfSupplyId).collect(Collectors.toList());
            // 批量对应得分配关系关联demand信息
            List<RzzFulfillmentOutput> plannedSupplyFulfillment = fulfillmentOutputDataList.stream()
                    .filter(p -> p.getQty().compareTo(BigDecimal.ZERO) > 0)
                    .filter(f -> selfSupplyIds.contains(f.getSupplyId())).collect(Collectors.toList());
            List<String> demandIds = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandId).distinct().collect(Collectors.toList());

            // 分配需求类型
            List<String> demandTypes = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandType).distinct().collect(Collectors.toList());

            if (demandTypes.contains(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode())) {
                // 当前批量对应的客户订单
                List<RzzDemandOutput> customerOrderList = demandOutputDataList.stream()
                        .filter(p -> demandIds.contains(p.getSelfDemandId())).collect(Collectors.toList());
                // 单个虚拟物料直接构建成品的制造订单及需求，链式结构，合批在关键工序的虚拟物料
                List<String> selfDemandId = customerOrderList.stream()
                        .map(RzzDemandOutput::getSelfDemandId).collect(Collectors.toList());
                // 成品的制造订单
                WorkOrderPO workOrderPO;
                // 批量的某个supply
                RzzSupplyOutput rzzSupplyOutput = plannedSupply.get(0);
                String productSupplyId = rzzSupplyOutput.getProductStockPointId();
                // 批量供应的某个demand
                RzzDemandOutput rzzDemandOutput = customerOrderList.get(0);
                String productDemandId = rzzDemandOutput.getProductStockPointId();
                boolean whetherProduct = Boolean.FALSE;
                String productBaseId;
                if (productSupplyId.contains("virtual")) {
                    String[] split = productSupplyId.split(STR_JOIN_VALUE);
                    productBaseId = split[2];
                } else {
                    productBaseId = productSupplyId;
                }
                NewProductStockPointVO newProductStockPointVO = mpsAnalysisContext.getProductMap().getOrDefault(productBaseId, null);
                if (Objects.isNull(newProductStockPointVO)) {
                    log.warn("批次：{}，没有找到物料：{}", productionIntervalId, productBaseId);
                    continue;
                }
                MdsProductStockPointBaseVO pointBaseVO = mpsAnalysisContext.getProductStockPointBaseMap()
                        .getOrDefault(newProductStockPointVO.getProductCode(), null);
                if (null == pointBaseVO) {
                    log.warn("批次：{}，没有找到物料基础数据：{}", productionIntervalId, newProductStockPointVO.getProductCode());
                    continue;
                }
                String chain = productionIntervalOutput.getChain();
                if (!chain.equals(fixedValue)) {
                    // 构建链式成品制造订单
                    workOrderPO = buildWorkOrder(qty, demandOutputDataList,
                            selfDemandId, operationDate, productionIntervalId,
                            productionIntervalOutput, mpsAnalysisContext, Boolean.TRUE, deliveryPlanPublishedVOS);
                } else {
                    if (productDemandId.equals(productBaseId)) {
                        // 构建半品制造订单，等于直接发半片
                        workOrderPO = buildWorkOrder(qty, demandOutputDataList,
                                selfDemandId, operationDate, productionIntervalId,
                                productionIntervalOutput, mpsAnalysisContext, Boolean.TRUE, deliveryPlanPublishedVOS);
                    } else {
                        // 构建多级BOM对应半品的成品制造订单
                        BigDecimal cusQty = customerOrderList.stream()
                                .map(RzzDemandOutput::getQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 多个半品供应使用原始需求的需求数量汇总做总量制造订单数量
                        workOrderPO = buildWorkOrder(cusQty, demandOutputDataList,
                                selfDemandId, operationDate, productionIntervalId,
                                productionIntervalOutput, mpsAnalysisContext, Boolean.FALSE, deliveryPlanPublishedVOS);
                        whetherProduct = Boolean.TRUE;
                    }
                }
                if (null == workOrderPO) {
                    log.warn("制造批量构建成品制造订单为空：{}", productionIntervalId);
                    continue;
                }
                Date dueDate = workOrderPO.getDueDate();
                // 需求订单id
                String demandOrderId = workOrderPO.getId();
                // 通过批量的供给需求得到该需求下除了这个批量还有没有其他供应
                if (whetherProduct) {
                    List<String> demandSortIds = customerOrderList.stream()
                            .map(RzzDemandOutput::getSelfDemandId).distinct().sorted().collect(Collectors.toList());
                    // 过滤当前需求所有的供应 两种情况（单个物品-链式路径，多个物品-Y式路径）
                    List<RzzFulfillmentOutput> workOrderDemandFulfill = fulfillmentOutputDataList.stream()
                            .filter(p -> demandIds.contains(p.getDemandId())).collect(Collectors.toList());
                    List<String> demandSupplyIds = workOrderDemandFulfill.stream()
                            .map(RzzFulfillmentOutput::getSupplyId).collect(Collectors.toList());
                    // 非末道工序对应的信息，判断是否包含多个物料信息
                    List<RzzSupplyOutput> demandSupply = supplyOutputDataList.stream()
                            .filter(p -> p.getSupplyType().equals(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode()))
                            .filter(p -> demandSupplyIds.contains(p.getSelfSupplyId()))
                            .collect(Collectors.toList());
                    // 对供应的物料进行分组，单个物料直接递归出成品客户订单构建制造订单，多个物品查找到对应生产批量构建关联关系
                    Map<String, List<RzzSupplyOutput>> selfProductionMap = demandSupply.stream()
                            .collect(Collectors.groupingBy(RzzSupplyOutput::getProductStockPointId));
                    // selfProductionMap 多个物料查找其余供应的制造批量信息，这个物料可能是半品信息，对半品信息直接构建对应的制造订单，另外一个半品查找对应的计划批量信息构建制造订单
                    RzzSupplyOutput batchPlanSupply = plannedSupply.get(0);
                    List<WorkOrderPO> semiWorkOrderPOList = new ArrayList<>();
                    boolean whetherComplete = false;
                    List<String> semiIds = new ArrayList<>();
                    for (Map.Entry<String, List<RzzSupplyOutput>> entry : selfProductionMap.entrySet()) {
                        String productId = entry.getKey();
                        // 如果物料id=批量的物料id直接创建当前批量的制造订单
                        if (batchPlanSupply.getProductStockPointId().equals(productId)) {
                            List<WorkOrderPO> semi = semiWorkOrderPOList.stream().filter(p -> p.getProductId().equals(productBaseId)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(semi)) {
                                whetherComplete = true;
                                continue;
                            }
                            // 构建当前半品需求的制造订单及demand
                            semiFinishedWorkOrder(productBaseId, dueDate, demandOrderId, operationDate, qty,
                                    productionIntervalId, mpsAnalysisContext, productionIntervalOutput, workOrderPO, semiWorkOrderPOList);
                        }
                        // 半品的制造批量信息，plannedSupply是循环识别到的批量，value也包含plannedSupply的制造批量供应信息
                        List<RzzSupplyOutput> value = entry.getValue();
                        List<String> selfPlannedIds = value.stream()
                                .map(RzzSupplyOutput::getSelfPlannedId).collect(Collectors.toList());
                        // 用value映射制造批量关系信息
                        List<String> productionIntervalIds = productionPlannedInIntervalOutputDataList.stream()
                                .filter(p -> selfPlannedIds.contains(p.getProductionPlannedId())).collect(Collectors.toList())
                                .stream().map(RzzProductionPlannedInIntervalOutput::getProductionIntervalId).distinct().collect(Collectors.toList());
                        // id转换，半品可能最后一道工序不是关键工序，转换为真正的物品id
                        String productStockPointId;
                        if (value.get(0).getProductStockPointId().contains("virtual")) {
                            String[] split = value.get(0).getProductStockPointId().split(STR_JOIN_VALUE);
                            productStockPointId = split[2];
                        } else {
                            productStockPointId = value.get(0).getProductStockPointId();
                        }
                        semiIds.addAll(productionIntervalIds);
                        // 循环批次
                        for (String intervalId : productionIntervalIds) {
                            List<String> intervalDemand = intervalDemandMap.get(intervalId);
                            if (CollectionUtils.isEmpty(intervalDemand) || CollectionUtils.isEmpty(demandSortIds)) {
                                log.error("批量id：{}，找不到对应原始需求", intervalId);
                                continue;
                            }
                            List<WorkOrderPO> semi = semiWorkOrderPOList.stream().filter(p -> p.getProductId().equals(productStockPointId)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(semi)) {
                                whetherComplete = true;
                                continue;
                            }
                            processIntervalMap.put(intervalId, intervalId);
                            // 创建关联半品的制造的订单及需求
                            RzzProductionIntervalOutput intervalOutput = intervalOutputMap.get(intervalId);
                            BigDecimal semiFinishedQty = intervalOutput.getQty();
                            // 创建另外半品的制造订单及需求
                            semiFinishedWorkOrder(productStockPointId, dueDate, demandOrderId, operationDate,
                                    semiFinishedQty, intervalId, mpsAnalysisContext, productionIntervalOutput, workOrderPO, semiWorkOrderPOList);
                        }
                    }
                    if (whetherComplete) {
                        log.warn("解析异常批次：{}，多级BOM需求未成套：{}", productionIntervalId, JSONUtil.toJsonStr(semiIds));
                    }
                    if (CollectionUtils.isEmpty(semiWorkOrderPOList)) {
                        log.warn("成品：{}，没有找到半成品的制造订单", workOrderPO.getId());
                    } else {
                        // 成品的数量由半品最多的数量订单决定
                        semiWorkOrderPOList.sort(Comparator.comparing(WorkOrderPO::getQuantity).reversed());
                        workOrderPO.setQuantity(semiWorkOrderPOList.get(0).getQuantity());
                        workOrderPO.setRemark(semiWorkOrderPOList.get(0).getRemark());
                    }
                    mpsAnalysisContext.getCreateWorkOrderList().add(workOrderPO);
                }
            } else {
                log.warn("生产批量：{}，没有找到供应需求信息", productionIntervalId);
            }
        }
        afterProcessDueDate(productionIntervalUpdateDuedateList, inIntervalMpa, supplyOutputDataList,
                fulfillmentOutputDataList, demandOutputDataList, mpsAnalysisContext, mpsAlgorithmOutput);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("MPS中间结果解析完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime1, new Date()));
        log.info("MPS解析结束");
    }

    private void afterProcessDueDate(List<RzzProductionIntervalOutput> productionIntervalUpdateDuedateList,
                                     Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMpa,
                                     List<RzzSupplyOutput> supplyOutputDataList,
                                     List<RzzFulfillmentOutput> fulfillmentOutputDataList,
                                     List<RzzDemandOutput> demandOutputDataList, MpsAnalysisContext mpsAnalysisContext,
                                     RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        log.info("后处理MPS更新固定制造订单交期开始，数量：{}", productionIntervalUpdateDuedateList.size());
        if (CollectionUtils.isEmpty(productionIntervalUpdateDuedateList)) {
            return;
        }
        Map<String, WorkOrderPO> workOrderMap = mpsAnalysisContext.getWorkOrderMap();
        PlanningHorizonVO planningHorizonVO = mpsAnalysisContext.getPlanningHorizonVO();
        Date planEndTime = planningHorizonVO.getPlanEndTime();
        List<WorkOrderPO> workOrderPOS = new ArrayList<>();
        // 合并workOrder更新交期数据
        List<ProductionPlannedMergeMapping> productionPlannedMergeMappings = mpsAlgorithmOutput.getProductionPlannedMergeMappingList();
        log.info("算法解析更新交期缓存mapping关系数量：{}", productionPlannedMergeMappings.size());
        Map<String, List<ProductionPlannedMergeMapping>> mergeOrderMap = StreamUtils.mapListByColumn(productionPlannedMergeMappings,
                ProductionPlannedMergeMapping::getWorkOrderId);
        for (RzzProductionIntervalOutput productionIntervalOutput : productionIntervalUpdateDuedateList) {
            String productionIntervalId = productionIntervalOutput.getProductionIntervalId();
            List<ProductionPlannedMergeMapping> mergeProductionPlannedMergeMappings = mergeOrderMap.get(productionIntervalId);
            if (!inIntervalMpa.containsKey(productionIntervalId)) {
                log.warn("固定制造订单找不到批量关系-更新计划结束期间交期：{}", productionIntervalId);
                getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                continue;
            }
            List<RzzProductionPlannedInIntervalOutput> inInterval = inIntervalMpa.get(productionIntervalId);
            List<String> plannedIds = inInterval.stream().map(RzzProductionPlannedInIntervalOutput::getProductionPlannedId)
                    .collect(Collectors.toList());
            // 生产批量对应得supply信息
            List<RzzSupplyOutput> plannedSupply = supplyOutputDataList.stream()
                    .filter(s -> plannedIds.contains(s.getSelfPlannedId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(plannedSupply)) {
                log.warn("固定制造订单找不到供应信息-更新计划结束期间交期：{}", productionIntervalId);
                getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                continue;
            }
            List<String> selfSupplyIds = plannedSupply.stream()
                    .map(RzzSupplyOutput::getSelfSupplyId).collect(Collectors.toList());
            // 批量对应得分配关系关联demand信息
            List<RzzFulfillmentOutput> plannedSupplyFulfillment = fulfillmentOutputDataList.stream()
                    .filter(p -> p.getQty().compareTo(BigDecimal.ZERO) > 0)
                    .filter(f -> selfSupplyIds.contains(f.getSupplyId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(plannedSupplyFulfillment)) {
                log.warn("固定制造订单找不到供需关系-更新计划结束期间交期：{}", productionIntervalId);
                getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                continue;
            }
            List<String> demandIds = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandId).distinct().sorted().collect(Collectors.toList());
            // 分配需求类型
            List<String> demandTypes = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandType).distinct().collect(Collectors.toList());

            if (demandTypes.contains(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode())) {
                // 当前批量对应的客户订单
                List<RzzDemandOutput> customerOrderList = demandOutputDataList.stream()
                        .filter(d -> demandIds.contains(d.getSelfDemandId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(customerOrderList)) {
                    log.warn("固定制造订单找不到原始需求-更新计划结束期间交期：{}", productionIntervalId);
                    getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                    continue;
                }
                customerOrderList.sort(Comparator.comparing(RzzDemandOutput::getDemandTime));
                RzzDemandOutput rzzDemandOutput = customerOrderList.get(0);
                String demandTime = rzzDemandOutput.getDemandTime();
                if (!workOrderMap.containsKey(productionIntervalId)) {
                    log.warn("固定制造订单找不到原始制造订单-更新计划结束期间交期：{}", productionIntervalId);
                    continue;
                }
                getUpdateDueDateOrder(DateUtils.stringToDate(demandTime, DateUtils.COMMON_DATE_STR1), workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
            }
        }
        if (CollectionUtils.isNotEmpty(workOrderPOS)) {
            BasePOUtils.updateBatchFiller(workOrderPOS);
            Lists.partition(workOrderPOS, 1000).forEach(p -> workOrderDao.updateBatch(p));
        }
        log.info("二次排程待更新固定制造订单交期数量：{}", workOrderPOS.size());
    }

    private void getUpdateDueDateOrder(Date demandTime, List<WorkOrderPO> workOrderPOS,
                                       Map<String, WorkOrderPO> workOrderMap,
                                       List<ProductionPlannedMergeMapping> mergeProductionPlannedMergeMappings) {

        for (ProductionPlannedMergeMapping mergeProductionPlannedMergeMapping : mergeProductionPlannedMergeMappings) {
            String mergeWorkOrderId = mergeProductionPlannedMergeMapping.getMergeWorkOrderId();
            WorkOrderPO updateWorkOrder = workOrderMap.get(mergeWorkOrderId);
            if (null == updateWorkOrder) {
                log.warn("更新交期订单找不到对应制造订单: {}", mergeWorkOrderId);
                continue;
            }
            String fixed = Optional.ofNullable(updateWorkOrder.getFixed()).orElse(YesOrNoEnum.NO.getCode());
            // 已开始，已完工的workOrder不更新交期
            if (limitStatus.contains(updateWorkOrder.getPlanStatus()) || YesOrNoEnum.YES.getCode().equals(fixed)) {
                continue;
            }
            updateWorkOrder.setDueDate(demandTime);
            updateWorkOrder.setRemark(updateWorkOrder.getRemark() + "；更新交期订单");
            workOrderPOS.add(updateWorkOrder);
            log.info("交期更新订单id：{}", updateWorkOrder.getId());
            String parentId = updateWorkOrder.getParentId();
            if (StrUtil.isNotEmpty(parentId)) {
                WorkOrderPO parentWorkOrder = workOrderMap.get(parentId);
                if (null != parentWorkOrder) {
                    parentWorkOrder.setDueDate(demandTime);
                    parentWorkOrder.setRemark(parentWorkOrder.getRemark() + "；更新交期订单");
                    workOrderPOS.add(parentWorkOrder);
                    log.info("交期更新父订单id：{}", parentWorkOrder.getId());
                }
            }
        }
    }

    private void semiFinishedWorkOrder(String productId,
                                       Date dueDate,
                                       String demandOrderId,
                                       Date operationDate,
                                       BigDecimal qty,
                                       String productionIntervalId,
                                       MpsAnalysisContext mpsAnalysisContext,
                                       RzzProductionIntervalOutput productionIntervalOutput,
                                       WorkOrderPO workOrderPO, List<WorkOrderPO> semiWorkOrderPOList) {
        // productId为半品供应的id，创建半品的制造订单
        NewProductStockPointVO newProductStockPointVO = mpsAnalysisContext.getProductMap().get(productId);
        if (null == newProductStockPointVO) {
            log.warn("成品的半品发货计划物品对应信息不存在：{}", productId);
            return;
        }
        NewStockPointVO newStockPointVO = mpsAnalysisContext.getStockMap().get(newProductStockPointVO.getStockPointCode());
        RoutingVO routingVO = mpsAnalysisContext.getRoutingVOMap().get(newProductStockPointVO.getId());
        if (null == routingVO) {
            log.warn("成品的半品发货计划物品对应工艺路径信息不存在：{}", productId);
            return;
        }
        if (!mpsAnalysisContext.getWorkOrderMap().containsKey(productionIntervalId)) {
            String workOrderCode = getWorkOrderCode(mpsAnalysisContext.getRuleEncodingsMap());
            // 创建workOrder
            String workOrderId = UUIDUtil.getUUID();
            WorkOrderPO workOrderInsert = new WorkOrderPO();
            workOrderInsert.setId(workOrderId);
            workOrderInsert.setOrderNo(workOrderCode);
            workOrderInsert.setQuantity(qty);
            workOrderInsert.setDueDate(dueDate);
            workOrderInsert.setProductId(productId);
            workOrderInsert.setProductStockPointId(productId);
            workOrderInsert.setCreateTime(operationDate);
            workOrderInsert.setModifyTime(operationDate);
            workOrderInsert.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
            workOrderInsert.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
            workOrderInsert.setKitStatus(KitStatusEnum.UNKIT.getCode());
            workOrderInsert.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            workOrderInsert.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            workOrderInsert.setCreateTime(operationDate);
            workOrderInsert.setModifyTime(operationDate);
            workOrderInsert.setEnabled(YesOrNoEnum.YES.getCode());
            workOrderInsert.setRoutingId(routingVO.getId());
            workOrderInsert.setRemark(productionIntervalId);
            workOrderInsert.setBomType(CHILD_BOM);
            workOrderInsert.setParentId(demandOrderId);
            workOrderInsert.setTopOrderId(demandOrderId);
            workOrderInsert.setDemandCategory(workOrderPO.getDemandCategory());
            workOrderInsert.setOrderType(workOrderPO.getOrderType());
            workOrderInsert.setEndingInventoryMinSafeDiff(workOrderPO.getEndingInventoryMinSafeDiff());
            mpsAnalysisContext.getCreateWorkOrderList().add(workOrderInsert);
            semiWorkOrderPOList.add(workOrderInsert);
            mpsAnalysisContext.getWorkOrderOnResourceMap().put(workOrderInsert.getId(),
                    mpsAnalysisContext.getWorkOrderOnResourceMap().get(productionIntervalOutput.getStandardResourceId()));
            // 创建demand
            String customerOrderDemandCode = getCustomerOrderDemandCode(mpsAnalysisContext.getRuleEncodingsMap());
            String demandId = UUIDUtil.getUUID();
            DemandPO demandPO = new DemandPO();
            demandPO.setId(demandId);
            demandPO.setProductId(productId);
            demandPO.setProductStockPointId(productId);
            demandPO.setDemandCode(customerOrderDemandCode);
            demandPO.setDemandOrderId(demandOrderId);
            demandPO.setDemandType(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode());
            demandPO.setQuantity(qty);
            demandPO.setUnfulfilledQuantity(BigDecimal.ZERO);
            demandPO.setDemandTime(dueDate);
            demandPO.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
            demandPO.setFulfillmentStatus(FulfillmentStatusEnum.ALL_FULFILLED.getCode());
            demandPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            demandPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            demandPO.setEnabled(YesOrNoEnum.YES.getCode());
            demandPO.setCreateTime(operationDate);
            demandPO.setModifyTime(operationDate);
            demandPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getCreateDemandsList().add(demandPO);
            // 创建supply
            String supplyCode = getOrderCode(mpsAnalysisContext.getRuleEncodingsMap(), RuleEncodingsEnum.WORK_ORDER_SUPPLY.getDesc());
            String supplyId = UUIDUtil.getUUID();
            SupplyPO supplyPO = new SupplyPO();
            supplyPO.setId(supplyId);
            supplyPO.setProductId(productId);
            supplyPO.setProductStockPointId(productId);
            supplyPO.setSupplyCode(supplyCode);
            supplyPO.setSupplyOrderId(workOrderId);
            supplyPO.setSupplyType(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode());
            supplyPO.setQuantity(qty);
            supplyPO.setUnfulfilledQuantity(BigDecimal.ZERO);
            supplyPO.setSupplyTime(dueDate);
            supplyPO.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
            supplyPO.setFulfillmentStatus(FulfillmentStatusEnum.ALL_FULFILLED.getCode());
            supplyPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            supplyPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            supplyPO.setCreateTime(operationDate);
            supplyPO.setModifyTime(operationDate);
            supplyPO.setEnabled(YesOrNoEnum.YES.getCode());
            supplyPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getCreateSupplyList().add(supplyPO);
            // 创建fulfillment
            String stockPointId = null == newStockPointVO ? null : newStockPointVO.getId();
            FulfillmentPO fulfillmentPO = new FulfillmentPO();
            fulfillmentPO.setId(UUIDUtil.getUUID());
            fulfillmentPO.setSupplyId(supplyId);
            fulfillmentPO.setDemandId(demandId);
            fulfillmentPO.setFulfillmentQuantity(qty);
            fulfillmentPO.setSupplyOrderId(workOrderId);
            fulfillmentPO.setDemandOrderId(demandOrderId);
            fulfillmentPO.setDemandType(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode());
            fulfillmentPO.setSupplyType(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode());
            fulfillmentPO.setDemandProductId(productId);
            fulfillmentPO.setDemandProductStockPointId(productId);
            fulfillmentPO.setSupplyProductId(productId);
            fulfillmentPO.setSupplyProductStockPointId(productId);
            fulfillmentPO.setDemandStockPointId(stockPointId);
            fulfillmentPO.setSupplyStockPointId(stockPointId);
            fulfillmentPO.setCreateTime(operationDate);
            fulfillmentPO.setModifyTime(operationDate);
            fulfillmentPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            fulfillmentPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            fulfillmentPO.setEnabled(YesOrNoEnum.YES.getCode());
            fulfillmentPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getCreateFulfillmentList().add(fulfillmentPO);
        } else {
            // 待修改的制造订单
            WorkOrderPO workOrderOld = mpsAnalysisContext.getWorkOrderMap().get(productionIntervalId);
            workOrderOld.setRoutingId(routingVO.getId());
            workOrderOld.setQuantity(qty);
            workOrderOld.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            workOrderOld.setModifyTime(operationDate);
            workOrderOld.setDueDate(dueDate);
            workOrderOld.setBomType(CHILD_BOM);
            workOrderOld.setDemandCategory(workOrderPO.getDemandCategory());
            workOrderOld.setOrderType(workOrderPO.getOrderType());
            workOrderOld.setEndingInventoryMinSafeDiff(workOrderPO.getEndingInventoryMinSafeDiff());
            workOrderOld.setRemark(productionIntervalId);
            // 修改的制造订单
            mpsAnalysisContext.getUpdateWorkOrderList().add(workOrderOld);
            // 修改半品制造订单对应的供需关系信息（数量，交期）
            List<FulfillmentPO> fulfillmentPOS = mpsAnalysisContext.getFulfillmentPOMap().get(workOrderOld.getId());
            if (CollectionUtils.isEmpty(fulfillmentPOS)) {
                return;
            }
            FulfillmentPO fulfillmentPO = fulfillmentPOS.get(0);
            fulfillmentPO.setFulfillmentQuantity(qty);
            fulfillmentPO.setModifyTime(operationDate);
            fulfillmentPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            fulfillmentPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getUpdateFulfillmentList().add(fulfillmentPO);

            String supplyId = fulfillmentPO.getSupplyId();
            String demandId = fulfillmentPO.getDemandId();
            if (mpsAnalysisContext.getSupplyPOMap().containsKey(supplyId)) {
                SupplyPO supplyPO = mpsAnalysisContext.getSupplyPOMap().get(supplyId);
                supplyPO.setQuantity(qty);
                supplyPO.setSupplyTime(dueDate);
                supplyPO.setModifyTime(operationDate);
                supplyPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                supplyPO.setRemark(productionIntervalId);
                mpsAnalysisContext.getUpdateSupplyList().add(supplyPO);
            }
            if (mpsAnalysisContext.getDemandMap().containsKey(demandId)) {
                DemandPO demandPO = mpsAnalysisContext.getDemandMap().get(demandId);
                demandPO.setQuantity(qty);
                demandPO.setDemandTime(dueDate);
                demandPO.setModifyTime(operationDate);
                demandPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                demandPO.setRemark(productionIntervalId);
                mpsAnalysisContext.getUpdateDemandsList().add(demandPO);
            }

        }
    }

    private String getDemandCategoryType(List<DeliveryPlanPublishedVO> deliverDemandList) {
        if (CollectionUtils.isNotEmpty(deliverDemandList)) {
            List<DeliveryPlanPublishedVO> demandTypeList = deliverDemandList.stream().filter(p -> StrUtil.isNotEmpty(p.getDemandCategory())
                            && p.getDemandCategory().equals(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(demandTypeList)) {
                return ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode();
            }
        }
        return null;
    }

    private int getEndingInventoryMinSafeDiff(List<DeliveryPlanPublishedVO> deliverDemandList, MpsAnalysisContext mpsAnalysisContext) {
        deliverDemandList.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));

        DeliveryPlanPublishedVO firstDemand = deliverDemandList.get(0);
        DeliveryPlanPublishedVO lastDemand = deliverDemandList.get(deliverDemandList.size() - 1);

        String oemCode = firstDemand.getOemCode();
        String productCode = firstDemand.getProductCode();
        Date demandTime = firstDemand.getDemandTime();
        Date lastDemandTime = lastDemand.getDemandTime();

        String key = StrUtil.join(STR_JOIN_VALUE, oemCode, productCode);

        List<InventoryShiftVO> inventoryShiftVOS = mpsAnalysisContext.getInventoryShiftMap().getOrDefault(key, ListUtil.empty())
                .stream()
                .filter(p -> p.getDeliveryDate().getTime() >= demandTime.getTime()
                        && p.getDeliveryDate().getTime() <= lastDemandTime.getTime()
                        && p.getEndingInventoryMinSafeDiff() != null)
                .collect(Collectors.toList());
        OptionalInt minEndingInventoryMinSafeDiff = inventoryShiftVOS.stream()
                .mapToInt(InventoryShiftVO::getEndingInventoryMinSafeDiff)
                .min();
        if (minEndingInventoryMinSafeDiff.isPresent()) {
            return minEndingInventoryMinSafeDiff.getAsInt();
        }
        return 0;
    }

    private WorkOrderPO buildWorkOrder(BigDecimal qty, List<RzzDemandOutput> demandOutputDataList,
                                       List<String> demandIds,
                                       Date operationDate, String productionIntervalId,
                                       RzzProductionIntervalOutput productionIntervalOutput,
                                       MpsAnalysisContext mpsAnalysisContext, Boolean whetherFinishProduct,
                                       List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS) {
        // 半品需求，最后一道工序为瓶颈工序直接构建客户订单及供应关系
        List<RzzDemandOutput> workOrderDemand = demandOutputDataList.stream()
                .filter(d -> demandIds.contains(d.getSelfDemandId()))
                .sorted(Comparator.comparing(RzzDemandOutput::getDemandTime)).collect(Collectors.toList());
        List<String> deliverDemandId = workOrderDemand.stream().map(RzzDemandOutput::getDemandId).collect(Collectors.toList());
        String demandCategoryType = null;
        int endingInventoryMinSafeDiff = 0;
        if (CollectionUtils.isNotEmpty(deliverDemandId)) {
            List<DeliveryPlanPublishedVO> deliverDemandList = deliveryPlanPublishedVOS.stream()
                    .filter(p -> deliverDemandId.contains(p.getId())).collect(Collectors.toList());
            // 判断需求类型
            if (CollectionUtils.isNotEmpty(deliverDemandList)) {
                demandCategoryType = getDemandCategoryType(deliverDemandList);
            }
            // 获取期末库存最小安全库存差值
            if (CollectionUtils.isNotEmpty(deliverDemandList)) {
                endingInventoryMinSafeDiff = getEndingInventoryMinSafeDiff(deliverDemandList, mpsAnalysisContext);
            }
        }
        // 合批后最早的需求订单
        RzzDemandOutput earliestDemand = workOrderDemand.get(0);
        String demandTime = earliestDemand.getDemandTime();
        // 如果存在原始需求时间，则使用原始需求时间，不存在则使用计算完提前期的时间
        String productStockPointId = earliestDemand.getProductStockPointId();
        NewProductStockPointVO newProductStockPointVO = mpsAnalysisContext.getProductMap().get(productStockPointId);
        if (null == newProductStockPointVO) {
            log.warn("成品发货计划物品对应信息不存在：{}", productStockPointId);
            return null;
        }
        NewStockPointVO newStockPointVO = mpsAnalysisContext.getStockMap().get(newProductStockPointVO.getStockPointCode());
        RoutingVO routingVO = mpsAnalysisContext.getRoutingVOMap().get(newProductStockPointVO.getId());
        if (null == routingVO) {
            log.warn("成品发货计划物品对应工艺路径信息不存在：{}", productStockPointId);
            return null;
        }
        Date dueDate = DateUtils.stringToDate(demandTime, DateUtils.COMMON_DATE_STR1);
        WorkOrderPO workOrder;
        if (whetherFinishProduct) {
            // 如果不包含该workOrder，则说明当前批次是新增的批次
            if (!mpsAnalysisContext.getWorkOrderMap().containsKey(productionIntervalId)) {
                String productId = newProductStockPointVO.getId();
                String workOrderId = UUIDUtil.getUUID();
                // 创建workOrder
                WorkOrderPO workOrderPOInsert = createWorkOrder(workOrderId, qty, dueDate, productId, operationDate,
                        newStockPointVO, mpsAnalysisContext, routingVO, productionIntervalId, productionIntervalOutput, CHAIN_BOM);
                workOrderPOInsert.setDemandCategory(demandCategoryType);
                workOrderPOInsert.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderPOInsert.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                // 创建批量
                createProductionInterval(productionIntervalOutput, mpsAnalysisContext,
                        workOrderId, productionIntervalId, operationDate, qty, productId);
                mpsAnalysisContext.getCreateWorkOrderList().add(workOrderPOInsert);
                workOrder = workOrderPOInsert;
                mpsAnalysisContext.getWorkOrderOnResourceMap().put(workOrderPOInsert.getId(),
                        mpsAnalysisContext.getPhysicalResourceCodeOnIdMap().get(productionIntervalOutput.getStandardResourceId()));
            } else {
                WorkOrderPO workOrderOld = mpsAnalysisContext.getWorkOrderMap().get(productionIntervalId);
                workOrderOld.setRoutingId(routingVO.getId());
                workOrderOld.setQuantity(qty);
                workOrderOld.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                workOrderOld.setModifyTime(operationDate);
                workOrderOld.setDueDate(dueDate);
                workOrderOld.setRemark(productionIntervalId);
                workOrderOld.setDemandCategory(demandCategoryType);
                workOrderOld.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderOld.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                // 修改的制造订单
                mpsAnalysisContext.getUpdateWorkOrderList().add(workOrderOld);

                ProductionIntervalPO productionIntervalPO = mpsAnalysisContext.getProductionIntervalMap().get(workOrderOld.getId());
                if (null != productionIntervalPO) {
                    setOther(productionIntervalPO, mpsAnalysisContext, productionIntervalOutput, operationDate);
                    productionIntervalPO.setQty(qty);
                    productionIntervalPO.setWhetherFixed(productionIntervalOutput.getFixed());
                    // 修改的计划批量
                    mpsAnalysisContext.getUpdateProductionIntervalList().add(productionIntervalPO);
                }
                workOrder = workOrderOld;
            }
        } else {
            if (!mpsAnalysisContext.getWorkOrderMap().containsKey(productionIntervalId)) {
                String productId = newProductStockPointVO.getId();
                String workOrderId = UUIDUtil.getUUID();
                // 创建半品对应的成品workOrder
                WorkOrderPO workOrderPOInsert = createWorkOrder(workOrderId, qty, dueDate, productId, operationDate,
                        newStockPointVO, mpsAnalysisContext, routingVO, productionIntervalId, productionIntervalOutput, TREE_BOM);
                workOrderPOInsert.setDemandCategory(demandCategoryType);
                workOrderPOInsert.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderPOInsert.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
//                mpsAnalysisContext.getCreateWorkOrderList().add(workOrderPOInsert);
                workOrder = workOrderPOInsert;
            } else {
                // 通过供需关系查找当前半品批次供给哪个制造订单，进行制造订单的修改操作，如果找不到，则创建新的制造订单
                WorkOrderPO workOrderOld = mpsAnalysisContext.getWorkOrderMap().get(productionIntervalId);
                String workOrderId = workOrderOld.getId();
                if (!mpsAnalysisContext.getFulfillmentPOMap().containsKey(workOrderId)) {
                    return null;
                }
                // 通过workOrderId查询分配关系，通过分配关系找到demand信息，再通过demand找到需求订单也就是成品订单，修改成品订单交期及时间
                List<FulfillmentPO> fulfillmentPOList = mpsAnalysisContext.getFulfillmentPOMap().get(workOrderId);
                String demandId = fulfillmentPOList.get(0).getDemandId();
                DemandPO demandPO = mpsAnalysisContext.getDemandMap().get(demandId);
                if (null == demandPO) {
                    return null;
                }
                // 当前半品供应的需求订单id
                String demandOrderId = demandPO.getDemandOrderId();
                // 待修改的制造订单
                WorkOrderPO fnishedProductWorkOrderPO = mpsAnalysisContext.getWorkOrderMap().get(demandOrderId);
                fnishedProductWorkOrderPO.setDueDate(dueDate);
                fnishedProductWorkOrderPO.setQuantity(qty);
                fnishedProductWorkOrderPO.setModifyTime(operationDate);
                fnishedProductWorkOrderPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                fnishedProductWorkOrderPO.setDemandCategory(demandCategoryType);
                fnishedProductWorkOrderPO.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                fnishedProductWorkOrderPO.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                mpsAnalysisContext.getUpdateWorkOrderList().add(fnishedProductWorkOrderPO);
                workOrder = fnishedProductWorkOrderPO;
            }
        }
        return workOrder;
    }

    private WorkOrderPO createWorkOrder(String workOrderId, BigDecimal qty, Date dueDate, String productId,
                                        Date operationDate, NewStockPointVO newStockPointVO,
                                        MpsAnalysisContext mpsAnalysisContext, RoutingVO routingVO, String productionIntervalId,
                                        RzzProductionIntervalOutput productionIntervalOutput, String bomType) {
        String workOrderCode = getWorkOrderCode(mpsAnalysisContext.getRuleEncodingsMap());
        WorkOrderPO workOrderPOInsert = new WorkOrderPO();
        workOrderPOInsert.setId(workOrderId);
        workOrderPOInsert.setOrderNo(workOrderCode);
        workOrderPOInsert.setQuantity(qty);
        workOrderPOInsert.setDueDate(dueDate);
        workOrderPOInsert.setProductId(productId);
        workOrderPOInsert.setProductStockPointId(productId);
        workOrderPOInsert.setCreateTime(operationDate);
        workOrderPOInsert.setModifyTime(operationDate);
        workOrderPOInsert.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
        workOrderPOInsert.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
        workOrderPOInsert.setKitStatus(KitStatusEnum.UNKIT.getCode());
        workOrderPOInsert.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
        workOrderPOInsert.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
        workOrderPOInsert.setCreateTime(operationDate);
        workOrderPOInsert.setModifyTime(operationDate);
        workOrderPOInsert.setEnabled(YesOrNoEnum.YES.getCode());
        workOrderPOInsert.setRoutingId(routingVO.getId());
        workOrderPOInsert.setRemark(productionIntervalId);
        workOrderPOInsert.setBomType(bomType);
        return workOrderPOInsert;
    }

    private void createProductionInterval(RzzProductionIntervalOutput productionIntervalOutput, MpsAnalysisContext mpsAnalysisContext,
                                          String workOrderId, String productionIntervalId, Date operationDate, BigDecimal qty, String productId) {
        // 创建批量
        ProductionIntervalPO productionIntervalPO = new ProductionIntervalPO();
        productionIntervalPO.setId(UUIDUtil.getUUID());
        productionIntervalPO.setProductionIntervalId(productionIntervalId);
        productionIntervalPO.setOrderId(workOrderId);
        productionIntervalPO.setProductStockPointId(productId);
        productionIntervalPO.setQty(qty);
        productionIntervalPO.setCreateTime(operationDate);
        productionIntervalPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
        setOther(productionIntervalPO, mpsAnalysisContext, productionIntervalOutput, operationDate);
        mpsAnalysisContext.getCreateProductionIntervalList().add(productionIntervalPO);
    }

    private void setOther(ProductionIntervalPO productionIntervalPO, MpsAnalysisContext mpsAnalysisContext,
                          RzzProductionIntervalOutput productionIntervalOutput, Date operationDate) {
        if (null == productionIntervalPO) {
            return;
        }
        productionIntervalPO.setStandardResourceId(productionIntervalOutput.getStandardResourceId());
        productionIntervalPO.setStartPeriod(productionIntervalOutput.getStartPeriod());
        productionIntervalPO.setEndPeriod(productionIntervalOutput.getEndPeriod());
        productionIntervalPO.setModifyTime(operationDate);
        productionIntervalPO.setWhetherFixed(productionIntervalOutput.getFixed());
        productionIntervalPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
    }


    private void databaseOperation(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date dateTime = new Date();
        List<WorkOrderPO> createWorkOrderList = mpsAnalysisContext.getCreateWorkOrderList();
        List<DemandPO> createDemandsList = mpsAnalysisContext.getCreateDemandsList();
        List<SupplyPO> createSupplyList = mpsAnalysisContext.getCreateSupplyList();
        List<ProductionIntervalPO> createProductionIntervalList = mpsAnalysisContext.getCreateProductionIntervalList();
        List<FulfillmentPO> createFulfillmentList = mpsAnalysisContext.getCreateFulfillmentList();
        Map<String, RuleEncodingsVO> ruleEncodingsMap = mpsAnalysisContext.getRuleEncodingsMap();

        List<WorkOrderPO> updateWorkOrderList = mpsAnalysisContext.getUpdateWorkOrderList();
        List<DemandPO> updateDemandsList = mpsAnalysisContext.getUpdateDemandsList();
        List<SupplyPO> updateSupplyList = mpsAnalysisContext.getUpdateSupplyList();
        List<FulfillmentPO> updateFulfillmentList = mpsAnalysisContext.getUpdateFulfillmentList();
        List<ProductionIntervalPO> updateProductionIntervalList = mpsAnalysisContext.getUpdateProductionIntervalList();
        List<String> waitingSyncWorkOrder = new ArrayList<>();
        log.info("创建workOrder数量：{}", createWorkOrderList.size());
        log.info("修改workOrder数量：{}", updateWorkOrderList.size());
        log.info("创建demand数量：{}", createDemandsList.size());
        log.info("修改demand数量：{}", updateDemandsList.size());
        log.info("创建supply数量：{}", createSupplyList.size());
        log.info("修改supply数量：{}", updateSupplyList.size());
        log.info("创建fulfillment数量：{}", createFulfillmentList.size());
        log.info("修改fulfillment数量：{}", updateFulfillmentList.size());
        log.info("创建ProductionInterval数量：{}", createProductionIntervalList.size());
        log.info("修改ProductionInterval数量：{}", updateProductionIntervalList.size());

        if (CollectionUtils.isNotEmpty(createWorkOrderList)) {
            waitingSyncWorkOrder.addAll(createWorkOrderList.stream().map(WorkOrderPO::getId).collect(Collectors.toList()));
            workOrderDao.insertBatch(createWorkOrderList);
            mpsAnalysisContext.getScheduleInfoMap().put("createWorkOrderList", createWorkOrderList);
        }
        if (CollectionUtils.isNotEmpty(createDemandsList)) {
            demandDao.insertBatch(createDemandsList);
        }
        if (CollectionUtils.isNotEmpty(createSupplyList)) {
            supplyDao.insertBatch(createSupplyList);
        }
        if (CollectionUtils.isNotEmpty(createFulfillmentList)) {
            fulfillmentDao.insertBatch(createFulfillmentList);
        }
        if (CollectionUtils.isNotEmpty(createProductionIntervalList)) {
            productionIntervalDao.insertBatch(createProductionIntervalList);
        }
        if (CollectionUtils.isNotEmpty(updateWorkOrderList)) {
            waitingSyncWorkOrder.addAll(updateWorkOrderList.stream().map(WorkOrderPO::getId).collect(Collectors.toList()));
            workOrderDao.updateBatch(updateWorkOrderList);
        }
        if (CollectionUtils.isNotEmpty(updateDemandsList)) {
            demandDao.updateBatch(updateDemandsList);
        }
        if (CollectionUtils.isNotEmpty(updateSupplyList)) {
            supplyDao.updateBatch(updateSupplyList);
        }
        if (CollectionUtils.isNotEmpty(updateFulfillmentList)) {
            fulfillmentDao.updateBatch(updateFulfillmentList);
        }
        if (CollectionUtils.isNotEmpty(updateProductionIntervalList)) {
            productionIntervalDao.updateBatch(updateProductionIntervalList);
        }
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc()));
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_SUPPLY.getDesc()));
        mpsAlgorithmOutput.getWorkOrderIds().addAll(waitingSyncWorkOrder);
        log.info("待二次同步制造订单数量：{}", waitingSyncWorkOrder.size());
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("MPS中间结果数据库操作完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
    }


    protected String getWorkOrderCode(Map<String, RuleEncodingsVO> ruleEncodingsMap) {
        RuleEncodingsVO prsRulRuleEncodingsVO = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc());
        String serialNumber = RuleEncodingsUtils.getSerialNumber(prsRulRuleEncodingsVO);
        String code = RuleEncodingsUtils.getCode(prsRulRuleEncodingsVO, null, serialNumber);
        prsRulRuleEncodingsVO.setSerialNumberMaxValue(serialNumber);
        ruleEncodingsMap.put(RuleEncodingsEnum.WORK_ORDER_NO.getDesc(), prsRulRuleEncodingsVO);
        return code;
    }

    private String getOrderCode(Map<String, RuleEncodingsVO> ruleEncodingsMap, String code) {
        RuleEncodingsVO customerOrderDemandRuleEncodingsVO = ruleEncodingsMap.get(code);
        String serialNumberCus = RuleEncodingsUtils.getSerialNumber(customerOrderDemandRuleEncodingsVO);
        String codeCus = RuleEncodingsUtils.getCode(customerOrderDemandRuleEncodingsVO, null, serialNumberCus);
        customerOrderDemandRuleEncodingsVO.setSerialNumberMaxValue(serialNumberCus);
        ruleEncodingsMap.put(code, customerOrderDemandRuleEncodingsVO);
        return codeCus;
    }


    private String getCustomerOrderDemandCode(Map<String, RuleEncodingsVO> ruleEncodingsMap) {
        RuleEncodingsVO customerOrderDemandRuleEncodingsVO = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc());
        String serialNumberCus = RuleEncodingsUtils.getSerialNumber(customerOrderDemandRuleEncodingsVO);
        String codeCus = RuleEncodingsUtils.getCode(customerOrderDemandRuleEncodingsVO, null, serialNumberCus);
        customerOrderDemandRuleEncodingsVO.setSerialNumberMaxValue(serialNumberCus);
        ruleEncodingsMap.put(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc(), customerOrderDemandRuleEncodingsVO);
        return codeCus;
    }


    /**
     * 1.取消（修改+待删除）的已计划制造订单
     * 2.删除被合批后的制造订单
     *
     * @param mpsAnalysisContext context
     */
    protected abstract void cancelPlan(MpsAnalysisContext mpsAnalysisContext);


}
