package com.yhl.scp.mps.adjust.strategy.impl;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.adjust.enums.AdjustPlanType;
import com.yhl.scp.mps.adjust.strategy.AbstractAdjustPlanStrategy;
import com.yhl.scp.mps.adjust.support.AdjustHWSupport;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.adjust.model.AdjustPlanContext;
import com.yhl.scp.mps.adjust.support.AdjustPlanSupport;
import com.yhl.scp.mps.schedule.service.HandworkScheduleService;
import com.yhl.scp.sds.basic.order.infrastructure.po.OperationSubTaskBasicPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationSubTaskPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationTaskPO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.OperationSubTaskDao;
import com.yhl.scp.sds.order.infrastructure.dao.OperationTaskDao;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>AdjustPlanMoldQuantityService</code>
 * <p>
 * 模具数量调整策略
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 10:38:25
 */
@Service
public class AdjustPlanMoldQuantityService extends AbstractAdjustPlanStrategy {

    @Resource
    private AdjustPlanSupport adjustPlanSupport;
    @Resource
    protected OperationDao operationDao;
    @Resource
    OperationTaskDao operationTaskDao;
    @Resource
    OperationSubTaskDao operationSubTaskDao;
    @Resource
    NewMdsFeign newMdsFeign;
    @Resource
    protected HandworkScheduleService handworkScheduleService;

    @Override
    protected String getServiceProviderName() {
        return AdjustPlanType.MOLD_QUANTITY.getCode();
    }

    @Override
    protected void doAdjust(RzzAdjustmentParam adjustmentParam) {
        AdjustPlanContext adjustPlanContext = adjustPlanSupport.initAdjustPlanContextResource(adjustmentParam);
        OperationPO adjustOperation = adjustPlanContext.getAdjustOperation();
        String parentId = adjustOperation.getId();
        List<OperationPO> subOperations = operationDao.selectByParentIds(Collections.singletonList(parentId));
        if (CollectionUtils.isEmpty(subOperations)) {
            return;
        }
        List<OperationPO> operationPOList = dataProcess(subOperations, adjustPlanContext);
        if (CollectionUtils.isEmpty(operationPOList)) {
            return;
        }
        List<RzzAdjustmentParam> adjustParams = AdjustHWSupport.getRzzAdjustmentParams(operationPOList, adjustmentParam);
        handworkScheduleService.doHandworkScheduleBatch(adjustParams);
    }

    private List<OperationPO> dataProcess(List<OperationPO> subOperations, AdjustPlanContext adjustPlanContext) {
        RzzAdjustmentParam adjustmentParam = adjustPlanContext.getAdjustmentParam();
        OperationPO adjustOperation = adjustPlanContext.getAdjustOperation();
        int oldMoldQty = subOperations.size();
        int newMoldQty = adjustmentParam.getMoldQty();
        String productId = adjustOperation.getProductId();
        List<NewProductStockPointVO> productStockPointVOS = newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(),
                Collections.singletonList(productId));
        if (CollectionUtils.isEmpty(productStockPointVOS)) {
            throw new BusinessException("对应产品不存在");
        }
        Integer moldQuantityLimit = productStockPointVOS.get(0).getMoldQuantityLimit();
        if (newMoldQty > moldQuantityLimit) {
//            throw new BusinessException("当前产品最模具数量不能超过" + moldQuantityLimit);
        }
        if (newMoldQty <= 0) {
            throw new BusinessException("模具数量不能小于等于0");
        }
        int diff = newMoldQty - oldMoldQty;
        // 没有增加或减少模具
        if (diff == 0) {
            return new ArrayList<>();
        }
        List<OperationPO> insertOperationList = new ArrayList<>();
        List<OperationPO> updateOperationList = new ArrayList<>();
        List<OperationTaskPO> insertOperationTaskList = new ArrayList<>();
        List<OperationSubTaskPO> insertOperationSubTaskList = new ArrayList<>();
        List<String> deleteOperationIdList = new ArrayList<>();
        // 更新模具数量变化生成相关数据
        subOperations = updateMold(diff, subOperations, insertOperationSubTaskList, insertOperationTaskList, updateOperationList, insertOperationList, deleteOperationIdList, adjustOperation, newMoldQty);
        // 现在subOperations所有数量已经根据模具数量重新计算了
        saveDate(updateOperationList, insertOperationList, insertOperationTaskList, insertOperationSubTaskList, deleteOperationIdList);
        return subOperations;
    }

    private List<OperationPO> updateMold(int diff, List<OperationPO> subOperations, List<OperationSubTaskPO> insertOperationSubTaskList,
                                         List<OperationTaskPO> insertOperationTaskList,List<OperationPO> updateOperationList, List<OperationPO> insertOperationList, List<String> deleteOperationIdList, OperationPO adjustOperation, int newMoldQty) {
        // 新增模具，需要新建对应的子operation, operation_task, operation_sub_task
        if (diff > 0) {
            subOperations.sort(Comparator.comparing(OperationPO::getOperationIndex));
            int operationIndex = subOperations.get(subOperations.size() - 1).getOperationIndex() + 2;
            // 新增模具，对应模具全按开始时间最早的子工序进行赋值
            OperationPO subOperationPO = subOperations.get(0);
            List<OperationTaskPO> operationTaskList = operationTaskDao.selectByParams(ImmutableMap.of("operationIds", Collections.singletonList(subOperationPO.getId())));
            List<OperationSubTaskPO> operationSubTaskList = operationSubTaskDao.selectByParams(ImmutableMap.of("operationIds", Collections.singletonList(subOperationPO.getId())));
            Map<String, List<OperationSubTaskPO>> subTaskMap = operationSubTaskList.stream().collect(Collectors.groupingBy(OperationSubTaskBasicPO::getTaskId));
            String[] split = subOperations.get(0).getOperationCode().split("-");
            for (int i = 0; i < diff; i++) {
                String operationCode = split[0] + "-" + split[1] + "-0" + operationIndex++;
                OperationPO operationPO = new OperationPO();
                String subOperationId = UUIDUtil.getUUID();
                BeanUtils.copyProperties(subOperationPO, operationPO);
                operationPO.setId(subOperationId);
                operationPO.setOperationCode(operationCode);
                for (OperationTaskPO operationTaskPO : operationTaskList) {
                    OperationTaskPO insertTask = new OperationTaskPO();
                    BeanUtils.copyProperties(operationTaskPO, insertTask);
                    insertTask.setId(UUIDUtil.getUUID());
                    List<OperationSubTaskPO> operationSubTaskPOS = subTaskMap.get(operationTaskPO.getId());
                    for (OperationSubTaskPO operationSubTaskPO : operationSubTaskPOS) {
                        OperationSubTaskPO insertSubTask = new OperationSubTaskPO();
                        BeanUtils.copyProperties(operationSubTaskPO, insertSubTask);
                        insertSubTask.setId(UUIDUtil.getUUID());
                        insertSubTask.setTaskId(insertTask.getId());
                        insertSubTask.setOperationId(subOperationId);
                        insertOperationSubTaskList.add(insertSubTask);
                    }
                    insertTask.setOperationId(subOperationId);
                    insertOperationTaskList.add(insertTask);
                }
                insertOperationList.add(operationPO);
                subOperations.add(operationPO);
            }
        }
        // 需要删掉模具，需要删除对应的子operation, operation_task, operation_sub_task
        else {
            subOperations.sort(Comparator.comparing(OperationPO::getOperationIndex).reversed());
            for (int i = 0; i < Math.abs(diff); i++) {
                OperationPO operationPO = subOperations.get(i);
                deleteOperationIdList.add(operationPO.getId());
            }
            subOperations = subOperations.stream().filter(t -> !deleteOperationIdList.contains(t.getId())).collect(Collectors.toList());
        }
        BigDecimal quantity = adjustOperation.getQuantity();

        BigDecimal size = new BigDecimal(newMoldQty);
        // 整除，减少循环次数
        BigDecimal quotient = quantity.divide(size, 0, RoundingMode.DOWN);
        // 取余
        BigDecimal remainder = quantity.remainder(size);
        for (OperationPO subOperation : subOperations) {
            subOperation.setQuantity(quotient);
            updateOperationList.add(subOperation);
        }
        while (remainder.compareTo(BigDecimal.ZERO) > 0){
            for (OperationPO operationPO : subOperations) {
                operationPO.setQuantity(operationPO.getQuantity().add(BigDecimal.ONE));
                remainder = remainder.subtract(BigDecimal.ONE);
                if (remainder.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
            }
        }
        return subOperations;
    }

    private void saveDate(List<OperationPO> updateOperationList,
                          List<OperationPO> insertOperationList, List<OperationTaskPO> insertOperationTaskList,
                          List<OperationSubTaskPO> insertOperationSubTaskList,
                          List<String> deleteOperationIdList) {
        if (CollectionUtils.isNotEmpty(insertOperationList)) {
            BasePOUtils.insertBatchFiller(insertOperationList);
            operationDao.insertBatch(insertOperationList);
        }
        if (CollectionUtils.isNotEmpty(updateOperationList)) {
            BasePOUtils.updateBatchFiller(updateOperationList);
            operationDao.updateBatch(updateOperationList);
        }
        if (CollectionUtils.isNotEmpty(insertOperationTaskList)) {
            BasePOUtils.insertBatchFiller(insertOperationTaskList);
            operationTaskDao.insertBatch(insertOperationTaskList);
        }
        if (CollectionUtils.isNotEmpty(insertOperationSubTaskList)) {
            BasePOUtils.insertBatchFiller(insertOperationSubTaskList);
            operationSubTaskDao.insertBatch(insertOperationSubTaskList);
        }
        if (CollectionUtils.isNotEmpty(deleteOperationIdList)) {
            operationDao.deleteBatch(deleteOperationIdList);
            operationTaskDao.deleteByOperationIds(deleteOperationIdList);
            operationSubTaskDao.deleteByOperationIds(deleteOperationIdList);
        }
    }
}
