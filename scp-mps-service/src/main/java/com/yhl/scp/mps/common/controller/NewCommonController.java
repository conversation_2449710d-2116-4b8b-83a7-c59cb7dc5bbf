package com.yhl.scp.mps.common.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.scp.biz.common.util.FileZipUtils;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mps.common.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <code>CommonController</code>
 * <p>
 * CommonController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/9 19:51
 */
@Slf4j
@Api(tags = "工具控制器")
@RestController
@RequestMapping("commonController")
public class NewCommonController extends BaseController {

    @javax.annotation.Resource
    private IpsFeign ipsFeign;

    public static final List<String> BASE_DIRECTORIES = Lists.newArrayList("/usr/local/dfp/workspace",
            "/usr/local/mps/workspace", "/usr/local/aps/workspace");

    @GetMapping("downloadZip")
    @ApiOperation(value = "下载文件")
    public ResponseEntity<Object> downloadZip(@RequestParam("logId") String logId) {
        AlgorithmLog algorithmLog = ipsFeign.selectAlgorithmLogById(logId).getData();
        if (Objects.isNull(algorithmLog)) {
            throw new BusinessException("算法日志信息无效：" + logId);
        }
        String path = algorithmLog.getFilePath();
        List<String> fileTypes = ListUtil.of("txt", "log", "xlsx", "csv", "json", "xls", "out","ser");
        return FileZipUtils.downloadZip(path, logId, BASE_DIRECTORIES, fileTypes);
    }

}
