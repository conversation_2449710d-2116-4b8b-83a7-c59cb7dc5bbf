package com.yhl.scp.dfp.massProduction.vo;

import java.io.Serializable;
import java.util.List;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <code>MassProductionHandoverVO</code>
 * <p>
 * 量产移交信息主表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:22:46
 */
@ApiModel(value = "量产移交信息主表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MassProductionHandoverVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 860451148063387268L;

    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @FieldInterpretation(value = "客户编码")
    private String customerCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String customerName;
    /**
     * 获取订单方式
     */
    @ApiModelProperty(value = "获取订单方式")
    @FieldInterpretation(value = "获取订单方式")
    private String orderAcquisitionMethod;
    /**
     * 订单联系人
     */
    @ApiModelProperty(value = "订单联系人")
    @FieldInterpretation(value = "订单联系人")
    private String orderContactPerson;
    /**
     * 发货地址
     */
    @ApiModelProperty(value = "发货地址")
    @FieldInterpretation(value = "发货地址")
    private String shipAddress;
    /**
     * 收货联系人
     */
    @ApiModelProperty(value = "收货联系人")
    @FieldInterpretation(value = "收货联系人")
    private String receivingContactPerson;
    /**
     * 库位代码（开票客户信息）
     */
    @ApiModelProperty(value = "库位代码（开票客户信息）")
    @FieldInterpretation(value = "库位代码（开票客户信息）")
    private String locationCode;
    /**
     * 内部车型代码
     */
    @ApiModelProperty(value = "内部车型代码")
    @FieldInterpretation(value = "内部车型代码")
    private String vehicleModelCode;
    /**
     * 车型企划量
     */
    @ApiModelProperty(value = "车型企划量")
    @FieldInterpretation(value = "车型企划量")
    private String vehicleModelVolume;
    /**
     * 特殊工艺说明
     */
    @ApiModelProperty(value = "特殊工艺说明")
    @FieldInterpretation(value = "特殊工艺说明")
    private String specialProcessInstructions;
    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    @FieldInterpretation(value = "审批状态")
    private String approvalStatus;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    
    /**
     * 审批流程id
     */
    @ApiModelProperty(value = "审批流程id")
    private String approvalId;
    
    @ApiModelProperty(value = "单据编号")
    private String documentCode;
    
    /**
     * 乘联品牌车型代码
     */
    @ApiModelProperty(value = "乘联品牌车型代码")
    @FieldInterpretation(value = "乘联品牌车型代码")
    private String vehicleModelBrand;
    
    /**
     * 量产移交信息详情
     */
    @ApiModelProperty(value = "量产移交信息详情")
    @FieldInterpretation(value = "量产移交信息详情")
    private List<MassProductionHandoverDetailVO> detailList;

    @Override
    public void clean() {

    }

}
