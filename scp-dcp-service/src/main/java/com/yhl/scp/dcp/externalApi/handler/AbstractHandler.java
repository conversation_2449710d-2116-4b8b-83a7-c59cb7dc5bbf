package com.yhl.scp.dcp.externalApi.handler;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.core.SequenceService;
import com.yhl.scp.dcp.sync.service.SyncCtrlService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <code>AbstractHandler</code>
 * <p>
 * AbstractHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-02 22:28:55
 */
@Slf4j
public abstract class AbstractHandler<T> implements Handler {

    @Autowired
    protected SyncCtrlService syncCtrlService;

    @Autowired
    protected ApiConfigService apiConfigService;

    @Autowired
    protected RedisUtil redisUtil;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    protected SequenceService sequenceService;

    @Resource
    protected IpsNewFeign ipsNewFeign;

    /**
     * 转化数据结构
     *
     * @param body 响应体
     * @return T
     */
    protected abstract T convertData(String body);

    /**
     * 处理具体内容
     *
     * @param apiConfigVO 接口对象
     * @param params      请求参数
     * @param t           映射对象
     * @return java.lang.String
     */
    protected abstract String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, T t);

    /**
     * 发送请求
     *
     * @param apiConfigVO 接口对象
     * @param params      请求参数
     * @return java.lang.String
     */
    protected abstract String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params);

    /**
     * 处理数据同步
     *
     * @param params 请求参数
     * @return java.lang.String
     */
    public String handle(Map<String, Object> params) {
        String redisKey = null;
        try {
            // 添加锁，对同一租户，同一笔请求上锁，防止重复提交引起数据重复
            String command = this.getCommand();
            redisKey = getRedisKey(command, params);
            ApiConfigVO apiConfigVO = apiConfigService.getByCommand(command);
            if (apiConfigVO == null) {
                throw new BusinessException("任务[" + command + "]对应的同步配置为空，请检查配置!");
            }
            // 判断同步类型，如果为增量，则进行增量同步
            if (this instanceof SyncDataHandler && DcpConstants.SYNC_TYPE_APPEND_SPLIT.equalsIgnoreCase(apiConfigVO.getSyncType())) {
                //增量统一同步
                log.info("===增量同步===");
                return splitSyncBatch(apiConfigVO, params);
            } else {
                //全量同步
                log.info("===非同步/全量同步===");
                String body = callApi(apiConfigVO, params);
                T t = convertData(body);
                return handleBody(apiConfigVO, params, t);
            }
        } finally {
            //释放锁
            if (StrUtil.isNotBlank(redisKey)) {
                this.redisUtil.delete(redisKey);
            }
        }
    }

    /**
     * 增量请求的情况下，需要拆分批次进行执行，单次同步不要超过7天
     *
     * @param apiConfigVO API配置
     * @param params
     * @return
     */
    private String splitSyncBatch(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        String lastUpdateDateStr = ((SyncDataHandler) this).getSyncRefValue(apiConfigVO, params);
        log.info("同步任务：{},参数:{},最后更新时间:{}", apiConfigVO, params, lastUpdateDateStr);
        Date currentDate = new Date();
        Date startDate = DateUtil.parse(lastUpdateDateStr);
        long period = DateUtil.between(startDate, new Date(), DateUnit.DAY);
        log.info("同步相隔时间:{}", period);

        // 以7天为一个同步批次
        long calculatePeriod = 7;
        long batchCnt = period / calculatePeriod + 1;
        log.info("拆分批次数量:{}", batchCnt);
        String result = "";

        for (int i = 0; i < batchCnt; i++) {
            int batchNo = i + 1;
            log.info("开始执行批次:{}-{}", batchCnt, batchNo);
            Date endTime = DateUtil.offsetDay(startDate, (int) calculatePeriod * batchNo);
            // 计算出来的日期不能超过当前日期
            if (DateUtil.compare(currentDate, endTime) < 0) {
                endTime = currentDate;
            }
            params.put(DcpConstants.SYNC_BATCH_END_TIME, endTime);
            params.put(DcpConstants.SYNC_BATCH_NO, batchNo);
            params.put(DcpConstants.SYNC_BATCH, batchCnt);

            String body = callApi(apiConfigVO, params);
            T t = convertData(body);
            result = handleBody(apiConfigVO, params, t);
            log.info("批次:{}-{},执行结果:{}", batchCnt, batchNo, result);
        }
        return result;
    }

    /**
     * 获取Redis的键，如果不需要锁定，则键为空
     *
     * @param command 指令
     * @param params  请求参数
     * @return java.lang.String
     */
    private String getRedisKey(String command, Map<String, Object> params) {
        if (this instanceof TokenHandler) {
            return null;
        }
        String redisKey = String.format(RedisKeyManageEnum.EXTERNAL_API.getKey(), encodeData(command, params));
        boolean locked = this.redisUtil.setIfAbsent(redisKey, redisKey, 120, TimeUnit.MINUTES);
        if (!locked) {
            throw new BusinessException("接口重复请求!");
        }
        return redisKey;
    }


    /**
     * 构建数据键值
     *
     * @param params 请求参数
     * @return java.lang.String
     */
    private String encodeData(String command, Map<String, Object> params) {
        StringBuffer keyBuffer = new StringBuffer();

        if (params.containsKey("scenario")) {
            Object scenarioObj = params.get("scenario");
            Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
            keyBuffer.append(scenarioMap.get("tenantId"));
            keyBuffer.append("#");
            keyBuffer.append(scenarioMap.get("dataBaseName"));
            keyBuffer.append("#");
            keyBuffer.append(SystemHolder.getUserId());
            keyBuffer.append("#");
            keyBuffer.append(command);
            keyBuffer.append("#");

            // 处理其他参数
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!"scenario".equals(entry.getKey())) {
                    keyBuffer.append(entry.getKey()).append("@").append(entry.getValue());
                    keyBuffer.append("#");
                }
            }
        } else {
            keyBuffer.append(SystemHolder.getTenantId());
            keyBuffer.append("#");
            keyBuffer.append(SystemHolder.getScenario());
            keyBuffer.append("#");
            keyBuffer.append(SystemHolder.getUserId());
            keyBuffer.append("#");
            keyBuffer.append(command);
            keyBuffer.append("#");

            for (Map.Entry<String, Object> entry : params.entrySet()) {
                keyBuffer.append(entry.getKey()).append("@").append(entry.getValue());
                keyBuffer.append("#");
            }
        }

        String md5Data = SecureUtil.md5(keyBuffer.toString());
        log.info("源数据:{},MD5运算后数据:{}", keyBuffer, md5Data);
        return md5Data;
    }

}
