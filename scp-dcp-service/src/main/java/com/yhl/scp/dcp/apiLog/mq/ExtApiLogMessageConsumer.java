package com.yhl.scp.dcp.apiLog.mq;

import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogMessageService;
import com.yhl.scp.dcp.common.constants.ExtApiLogMqConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ExtApiLogMessageConsumer</code>
 * <p>
 * 接口日志报文消息消费者
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Slf4j
@Component
public class ExtApiLogMessageConsumer {

    @Resource
    private ExtApiLogMessageService extApiLogMessageService;

    /**
     * 接收接口日志报文消息
     *
     * @param data 消息数据
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ExtApiLogMqConstants.EXT_API_LOG_MESSAGE_QUEUE, ignoreDeclarationExceptions = "true"),
            exchange = @Exchange(value = ExtApiLogMqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC),
            key = ExtApiLogMqConstants.EXT_API_LOG_MESSAGE_ROUTING_KEY
    ))
    public void receiveExtApiLogMessage(String data) {
        try {
            log.info("接收到接口日志报文消息：{}", data);
            
            if (StringUtils.isBlank(data)) {
                log.warn("接收到空的报文消息");
                return;
            }

            // 解析消息
            ExtApiLogMessageMQ message = JSONObject.parseObject(data, ExtApiLogMessageMQ.class);

            if (message == null || StringUtils.isBlank(message.getLogId())) {
                log.warn("报文消息格式错误或缺少logId");
                return;
            }

            // 处理报文数据
            extApiLogMessageService.processMessageData(
                    message.getLogId(),
                    message.getMessageData(),
                    message.getMessageType()
            );

            log.debug("处理接口日志报文消息成功，logId: {}, messageType: {}", 
                    message.getLogId(), message.getMessageType());

        } catch (Exception e) {
            log.error("处理接口日志报文消息失败：{}, error: {}", data, e.getMessage(), e);
        }
    }
}
