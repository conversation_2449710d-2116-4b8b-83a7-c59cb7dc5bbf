package com.yhl.scp.dcp.apiLog.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <code>ExtApiLogMessageConfig</code>
 * <p>
 * 接口日志报文处理配置
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Data
@Component
@ConfigurationProperties(prefix = "ext.api.log.message")
public class ExtApiLogMessageConfig {

    /**
     * 响应报文中数组字段
     */
    private List<String> responseArrayPaths = Arrays.asList(
            "data.message",
            "data.list",
            "data.items",
            "data.records",
            "data.result",
            "data.data",
            "result.data",
            "result.list",
            "result.items",
            "message",
            "list",
            "items",
            "records",
            "data"
    );

}
