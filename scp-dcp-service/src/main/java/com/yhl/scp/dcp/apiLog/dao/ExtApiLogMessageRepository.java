package com.yhl.scp.dcp.apiLog.dao;

import com.yhl.scp.dcp.apiLog.dto.ExtApiLogMessageDTO;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <code>ExtApiLogMessageRepository</code>
 * <p>
 * 接口日志报文数据MongoDB Repository
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Repository
public interface ExtApiLogMessageRepository extends MongoRepository<ExtApiLogMessageDTO, String> {

    /**
     * 根据日志ID查询报文数据
     *
     * @param logId 日志ID
     * @return 报文数据列表
     */
    List<ExtApiLogMessageDTO> findByLogId(String logId);

    /**
     * 根据日志ID和报文类型查询报文数据
     *
     * @param logId       日志ID
     * @param messageType 报文类型
     * @return 报文数据列表
     */
    List<ExtApiLogMessageDTO> findByLogIdAndMessageType(String logId, String messageType);

    /**
     * 根据日志ID删除报文数据
     *
     * @param logId 日志ID
     */
    void deleteByLogId(String logId);
}
