package com.yhl.scp.dcp.externalApi.handler.mes;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description:
 * author：zyh
 * date: 2024/11/13
 */
@Component
@Slf4j
public class ChainLineHandler extends SyncDataHandler<List<ChainLineInventoryLogDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private MpsFeign mpsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected List<ChainLineInventoryLogDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES同步链式生产线数据为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, ChainLineInventoryLogDTO.class);
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<ChainLineInventoryLogDTO> chainLineInventoryLogDTOS) {
        Date lastUpdateDate =
                chainLineInventoryLogDTOS.stream().map(ChainLineInventoryLogDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                List<ChainLineInventoryLogDTO> chainLineInventoryLogDTOList) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(chainLineInventoryLogDTOList)) {
            log.error("链式生产线数据为空");
            return null;
        }
        try {
            BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MPS.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "OP_BUSINESS_AREA", "EXTERNAL_REQ", null);
            String rangeData = scenarioBusinessRange.getData().getRangeData();
            if(StringUtils.isBlank(rangeData)){
                log.error("未找到对应的范围数据！");
                throw new BusinessException("未找到对应的范围数据！");
            }

            chainLineInventoryLogDTOList = chainLineInventoryLogDTOList.stream()
                    .filter(chainLineInventoryLogDTO -> rangeData.equals(chainLineInventoryLogDTO.getCompanyCode()))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    ChainLineInventoryLogDTO::getRelationId,
                                    dto -> dto,
                                    (dto1, dto2) -> dto1.getLastUpdateDate().after(dto2.getLastUpdateDate()) ? dto1 : dto2
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            Assert.isTrue(mdsScenario.getSuccess(), mdsScenario.getMsg());
            mpsFeign.handleChainLine(mdsScenario.getData(), chainLineInventoryLogDTOList);
            this.saveSyncCtrl(apiConfigVO, params, chainLineInventoryLogDTOList);
            return "同步成功";
        } catch (Exception e) {
            return "处理链式生产线时发生错误: " + e.getMessage();
        }
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES链式生产线:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri + "/" + systemNumber + "/"
                    + this.sequenceService.getSuffix(systemNumber, getCommand(), 5) + apiParams;
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},systemNumber={},url={}", mesToken, apiUri, apiParams, systemNumber,
                        url);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            String reqCode = "FY_PRO_LINE_RELATION_FOR_BPIM";
            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", 10000);
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                    paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步箱体信息失败！");
                    String body = responseEntity.getBody();
                    log.info("请求MES链式生产线完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                            beginTime = endTime;
                        } else {
                            currentPage++;
                        }
                    }

                }

            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.CHAIN_LINE.getCode());
    }

}
