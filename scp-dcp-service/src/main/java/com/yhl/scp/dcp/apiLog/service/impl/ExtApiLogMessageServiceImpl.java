package com.yhl.scp.dcp.apiLog.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dcp.apiLog.config.ExtApiLogMessageConfig;
import com.yhl.scp.dcp.apiLog.dao.ExtApiLogMessageRepository;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogMessageDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <code>ExtApiLogMessageServiceImpl</code>
 * <p>
 * 接口日志报文数据服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Slf4j
@Service
public class ExtApiLogMessageServiceImpl implements ExtApiLogMessageService {

    @Resource
    private ExtApiLogMessageRepository extApiLogMessageRepository;

    @Resource
    private ExtApiLogMessageConfig config;

    @Override
    public void saveRequestMessage(String logId, String requestBody) {
        if (StringUtils.isNotBlank(requestBody)) {
            processMessageData(logId, requestBody, "REQUEST");
        }
    }

    @Override
    public void saveResponseMessage(String logId, String responseBody) {
        if (StringUtils.isNotBlank(responseBody)) {
            processMessageData(logId, responseBody, "RESPONSE");
        }
    }

    @Override
    public void processMessageData(String logId, String messageData, String messageType) {
        try {
            if (StringUtils.isBlank(messageData)) {
                return;
            }

            if ("RESPONSE".equals(messageType)) {
                // 响应报文特殊处理
                processResponseMessage(logId, messageData);
            } else if ("REQUEST".equals(messageType)) {
                // 请求报文处理
                processRequestMessage(logId, messageData,messageType);
            } else {
                // 其他类型按通用方式处理
                processGenericMessage(logId, messageData, messageType);
            }

            log.debug("处理报文数据成功，logId: {}, messageType: {}", logId, messageType);

        } catch (Exception e) {
            log.error("处理报文数据失败，logId: {}, messageType: {}, error: {}",
                    logId, messageType, e.getMessage());
            // 如果处理失败，直接存储原始数据
            saveMessageItem(logId, messageData, messageType);
        }
    }

    /**
     * 处理响应报文：智能提取数组数据
     *
     * @param logId       日志ID
     * @param messageData 响应报文数据
     */
    private void processResponseMessage(String logId, String messageData) {
        try {
            JSONObject responseObj = JSON.parseObject(messageData);

            // 查找响应中的数组字段并提取
            JSONArray extractedArray = findAndExtractArrayFromResponse(responseObj);

            if (extractedArray != null && !extractedArray.isEmpty()) {

                // 找到数组，存储数组中的每个对象
                for (int i = 0; i < extractedArray.size(); i++) {
                    Object item = extractedArray.get(i);
                    saveMessageItem(logId, JSON.toJSONString(item), "RESPONSE");
                }
                log.debug("从响应中提取到数组，共{}个元素，logId: {}", extractedArray.size(), logId);


            } else {
                // 没有找到数组或数组为空，存储整个响应
//                saveMessageItem(logId, messageData, "RESPONSE");
                log.debug("响应中未找到数组数据，logId: {}", logId);

            }
        } catch (Exception e) {
            log.warn("解析响应报文失败，使用原始数据存储，logId: {}, error: {}", logId, e.getMessage());
            saveMessageItem(logId, messageData, "RESPONSE");
        }
    }

    /**
     * 查找并提取响应中的数组数据
     * 支持多种常见的响应结构模式
     *
     * @param responseObj 响应JSON对象
     * @return 提取到的数组，如果没有找到则返回null
     */
    private JSONArray findAndExtractArrayFromResponse(JSONObject responseObj) {
        // 使用配置中的数组路径
        List<String> arrayPaths = config.getResponseArrayPaths();

        // 按优先级查找数组字段
        for (String path : arrayPaths) {
            JSONArray array = getArrayByPath(responseObj, path);
            if (array != null && !array.isEmpty()) {
                log.debug("在路径 '{}' 找到数组数据，元素数量: {}", path, array.size());

                return array;
            }
        }

        // 如果启用递归查找且没有找到预定义路径的数组，尝试递归查找第一个非空数组
        JSONArray firstArray = findFirstNonEmptyArray(responseObj);
        if (firstArray != null) {
            log.debug("通过递归查找到数组数据，元素数量: {}", firstArray.size());

            return firstArray;
        }

        return null;
    }

    /**
     * 根据路径获取数组
     *
     * @param obj  JSON对象
     * @param path 路径，如 "data.message"
     * @return 数组对象，如果不存在或不是数组则返回null
     */
    private JSONArray getArrayByPath(JSONObject obj, String path) {
        try {
            String[] parts = path.split("\\.");
            Object current = obj;

            for (String part : parts) {
                if (current instanceof JSONObject) {
                    current = ((JSONObject) current).get(part);
                } else {
                    return null;
                }
            }

            return current instanceof JSONArray ? (JSONArray) current : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 递归查找第一个非空数组
     *
     * @param obj JSON对象
     * @return 第一个找到的非空数组，如果没有则返回null
     */
    private JSONArray findFirstNonEmptyArray(Object obj) {
        if (obj instanceof JSONArray) {
            JSONArray array = (JSONArray) obj;
            return array.isEmpty() ? null : array;
        } else if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            for (String key : jsonObj.keySet()) {
                Object value = jsonObj.get(key);
                JSONArray result = findFirstNonEmptyArray(value);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 处理请求报文：智能识别请求类型
     *
     * @param logId       日志ID
     * @param messageData 请求报文数据
     */
    private void processRequestMessage(String logId, String messageData, String messageType) {
        try {

                Object requestObject = JSON.parse(messageData);

                if (requestObject instanceof JSONArray) {
                    JSONArray jsonArray = (JSONArray) requestObject;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        Object item = jsonArray.get(i);
                        saveMessageItem(logId, JSON.toJSONString(item), messageType);
                    }

                } else{
                    saveMessageItem(logId, messageData, messageType);
                }
        } catch (Exception e) {
            log.warn("解析请求报文失败，使用原始数据存储，logId: {}, error: {}", logId, e.getMessage());
            saveMessageItem(logId, messageData, "REQUEST");
        }
    }


    /**
     * 通用报文处理：检查是否为数组
     *
     * @param logId       日志ID
     * @param messageData 报文数据
     * @param messageType 报文类型
     */
    private void processGenericMessage(String logId, String messageData, String messageType) {
        try {
            Object jsonObj = JSON.parse(messageData);

            if (jsonObj instanceof JSONArray) {
                // 如果是数组，拆分为对象存储
                JSONArray jsonArray = (JSONArray) jsonObj;
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object item = jsonArray.get(i);
                    saveMessageItem(logId, JSON.toJSONString(item), messageType);
                }
            } else {
                // 如果是对象，直接存储
                saveMessageItem(logId, messageData, messageType);
            }
        } catch (Exception e) {
            log.warn("解析报文失败，使用原始数据存储，logId: {}, messageType: {}, error: {}",
                    logId, messageType, e.getMessage());
            saveMessageItem(logId, messageData, messageType);
        }
    }

    /**
     * 保存单个报文项
     *
     * @param logId       日志ID
     * @param messageData 报文数据
     * @param messageType 报文类型
     */
    private void saveMessageItem(String logId, String messageData, String messageType) {
        try {
            ExtApiLogMessageDTO messageDTO = ExtApiLogMessageDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .logId(logId)
                    .messageType(messageType)
                    .messageData(messageData)
                    .createTime(new Date())
                    .build();

            extApiLogMessageRepository.save(messageDTO);

            log.debug("保存报文项成功，logId: {}, messageType: {}",
                    logId, messageType);
        } catch (Exception e) {
            log.error("保存报文项失败，logId: {}, messageType: {}, error: {}",
                    logId, messageType, e.getMessage());
        }
    }

    @Override
    public List<ExtApiLogMessageDTO> getMessagesByLogId(String logId) {
        return extApiLogMessageRepository.findByLogId(logId);
    }

    @Override
    public List<ExtApiLogMessageDTO> getMessagesByLogIdAndType(String logId, String messageType) {
        return extApiLogMessageRepository.findByLogIdAndMessageType(logId, messageType);
    }

    @Override
    public void deleteMessagesByLogId(String logId) {
        try {
            extApiLogMessageRepository.deleteByLogId(logId);
            log.debug("删除报文数据成功，logId: {}", logId);
        } catch (Exception e) {
            log.error("删除报文数据失败，logId: {}, error: {}", logId, e.getMessage());
        }
    }
}
