# 接口日志报文处理配置
ext:
  api:
    log:
      message:
        # 是否启用智能数组提取
        enable-smart-array-extraction: true
        
        # 数组拆分的最大元素数量
        max-array-size-for-split: 1000
        
        # 是否启用递归查找数组
        enable-recursive-array-search: true
        
        # 是否记录处理日志
        enable-processing-log: true
        
        # 响应报文中数组字段的查找路径（按优先级排序）
        response-array-paths:
          - "data.message"      # MES系统
          - "data.list"         # 通用列表
          - "data.items"        # 通用项目
          - "data.records"      # 通用记录
          - "data.result"       # 通用结果
          - "data.data"         # 嵌套数据
          - "result.data"       # 结果数据
          - "result.list"       # 结果列表
          - "result.items"      # 结果项目
          - "result.records"    # 结果记录
          - "response.data"     # 响应数据
          - "response.list"     # 响应列表
          - "message"           # 直接消息
          - "list"              # 直接列表
          - "items"             # 直接项目
          - "records"           # 直接记录
          - "data"              # 直接数据
        
        # 请求报文中数组字段的查找字段
        request-array-fields:
          - "data"
          - "list"
          - "items"
          - "records"
          - "params"
          - "conditions"
          - "filters"
          - "batch"
          - "operations"
        
        # 分页字段模式（每组字段都存在时认为是分页请求）
        pagination-patterns:
          - ["currentPage", "pageSize"]     # 标准分页
          - ["page", "size"]                # Spring分页
          - ["pageNum", "pageSize"]         # MyBatis分页
          - ["offset", "limit"]             # 偏移分页
          - ["start", "length"]             # DataTables分页
          - ["pageIndex", "pageSize"]       # 索引分页
          - ["pageNo", "pageSize"]          # 页码分页
        
        # 特殊请求标识字段（存在时直接存储整个请求）
        special-request-fields:
          - "reqCode"           # MES系统特有
          - "action"            # 操作类型
          - "method"            # 方法名
          - "command"           # 命令类型
