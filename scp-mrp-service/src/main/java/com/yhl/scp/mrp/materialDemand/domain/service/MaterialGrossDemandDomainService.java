package com.yhl.scp.mrp.materialDemand.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.materialDemand.domain.entity.MaterialGrossDemandDO;
import com.yhl.scp.mrp.materialDemand.infrastructure.dao.MaterialGrossDemandDao;
import com.yhl.scp.mrp.materialDemand.infrastructure.po.MaterialGrossDemandPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <code>MaterialGrossDemandDomainService</code>
 * <p>
 * 材料计划毛需求领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:51:59
 */
@Service
public class MaterialGrossDemandDomainService {

    @Resource
    private MaterialGrossDemandDao materialGrossDemandDao;

    /**
     * 数据校验
     *
     * @param materialGrossDemandDO 领域对象
     */
    public void validation(MaterialGrossDemandDO materialGrossDemandDO) {
        checkNotNull(materialGrossDemandDO);
        checkUniqueCode(materialGrossDemandDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialGrossDemandDO 领域对象
     */
    private void checkNotNull(MaterialGrossDemandDO materialGrossDemandDO) {

    }

    /**
     * 唯一性校验
     *
     * @param materialGrossDemandDO 领域对象
     */
    private void checkUniqueCode(MaterialGrossDemandDO materialGrossDemandDO) {

    }

    public MaterialGrossDemandPO createNewMaterialGrossDemandPO(MaterialGrossDemandPO materialGrossDemandPO) {
        MaterialGrossDemandPO newMaterialGrossDemandPO = new MaterialGrossDemandPO();
        newMaterialGrossDemandPO.setMaterialGrossDemandVersionId(materialGrossDemandPO.getMaterialGrossDemandVersionId());
        newMaterialGrossDemandPO.setMainProductCode(materialGrossDemandPO.getMainProductCode());
        newMaterialGrossDemandPO.setProductCode(materialGrossDemandPO.getProductCode());
        newMaterialGrossDemandPO.setProductName(materialGrossDemandPO.getProductName());
        newMaterialGrossDemandPO.setProductId(materialGrossDemandPO.getProductId());
        newMaterialGrossDemandPO.setProductClassify(materialGrossDemandPO.getProductClassify());
        newMaterialGrossDemandPO.setProductCategory(materialGrossDemandPO.getProductCategory());
        newMaterialGrossDemandPO.setProductFactoryCode(materialGrossDemandPO.getProductFactoryCode());
        newMaterialGrossDemandPO.setVehicleModeCode(materialGrossDemandPO.getVehicleModeCode());
        newMaterialGrossDemandPO.setInputFactor(materialGrossDemandPO.getInputFactor());
        newMaterialGrossDemandPO.setDemandTime(materialGrossDemandPO.getDemandTime());
        newMaterialGrossDemandPO.setDemandQuantity(materialGrossDemandPO.getDemandQuantity());
        newMaterialGrossDemandPO.setUnFulfillmentQuantity(materialGrossDemandPO.getUnFulfillmentQuantity());
        newMaterialGrossDemandPO.setOperationCode(materialGrossDemandPO.getOperationCode());
        newMaterialGrossDemandPO.setSupplyModel(materialGrossDemandPO.getSupplyModel());
        newMaterialGrossDemandPO.setProductColor(materialGrossDemandPO.getProductColor());
        newMaterialGrossDemandPO.setProductThickness(materialGrossDemandPO.getProductThickness());
        newMaterialGrossDemandPO.setDemandSource(materialGrossDemandPO.getDemandSource());
        newMaterialGrossDemandPO.setId(UUID.randomUUID().toString());
        return newMaterialGrossDemandPO;
    }
}
