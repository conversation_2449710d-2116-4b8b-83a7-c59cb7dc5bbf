package com.yhl.scp.mrp.inventory.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpOriginalFilmFFOnway;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPoCreate;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.supplier.vo.SupplierAddressVO;
import com.yhl.scp.mrp.excel.handler.CustomColumnWidthHandler;
import com.yhl.scp.mrp.freestorage.service.ProFreeStorageService;
import com.yhl.scp.mrp.freestorage.vo.ProFreeStorageVO;
import com.yhl.scp.mrp.inventory.convertor.InventoryFloatGlassShippedDetailConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryFloatGlassShippedDetailDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryFloatGlassShippedDetailDomainService;
import com.yhl.scp.mrp.inventory.dto.*;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryFloatGlassShippedDetailDao;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryQuayDetailDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryFloatGlassShippedDetailPO;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailValidateVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.plan.service.impl.NoGlassMrpServiceImpl;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryFloatGlassShippedDetailServiceImpl</code>
 * <p>
 * 原片浮法已发运库存批次明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:25:16
 */
@Slf4j
@Service
public class InventoryFloatGlassShippedDetailServiceImpl extends AbstractService implements InventoryFloatGlassShippedDetailService {

    @Resource
    private InventoryFloatGlassShippedDetailDao inventoryFloatGlassShippedDetailDao;
    @Resource
    private InventoryFloatGlassShippedDetailDomainService inventoryFloatGlassShippedDetailDomainService;
    @Resource
    private NoGlassMrpServiceImpl noGlassMrpService;
    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;
    @Resource
    private InventoryQuayDetailDao inventoryQuayDetailsDao;
    @Resource
    private ProFreeStorageService proFreeStorageService;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private NewMdsFeign newMdsFeign;


    @Override
    public BaseResponse<Void> doCreate(InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO) {
        // 0.数据转换
        InventoryFloatGlassShippedDetailDO inventoryFloatGlassShippedDetailDO = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Do(inventoryFloatGlassShippedDetailDTO);
        InventoryFloatGlassShippedDetailPO inventoryFloatGlassShippedDetailPO = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Po(inventoryFloatGlassShippedDetailDTO);
        // 1.数据校验
        inventoryFloatGlassShippedDetailDomainService.validation(inventoryFloatGlassShippedDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryFloatGlassShippedDetailPO);
        inventoryFloatGlassShippedDetailDao.insert(inventoryFloatGlassShippedDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO) {
        // 0.数据转换
        InventoryFloatGlassShippedDetailDO inventoryFloatGlassShippedDetailDO = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Do(inventoryFloatGlassShippedDetailDTO);
        InventoryFloatGlassShippedDetailPO inventoryFloatGlassShippedDetailPO = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Po(inventoryFloatGlassShippedDetailDTO);
        // 1.数据校验
        inventoryFloatGlassShippedDetailDomainService.validation(inventoryFloatGlassShippedDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryFloatGlassShippedDetailPO);
        inventoryFloatGlassShippedDetailDao.update(inventoryFloatGlassShippedDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryFloatGlassShippedDetailDTO> list) {
        List<InventoryFloatGlassShippedDetailPO> newList = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryFloatGlassShippedDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryFloatGlassShippedDetailDTO> list) {
        List<InventoryFloatGlassShippedDetailPO> newList = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryFloatGlassShippedDetailDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<InventoryFloatGlassShippedDetailDTO> list) {
        List<InventoryFloatGlassShippedDetailPO> newList = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryFloatGlassShippedDetailDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryFloatGlassShippedDetailDao.deleteBatch(idList);
        }
        return inventoryFloatGlassShippedDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryFloatGlassShippedDetailVO selectByPrimaryKey(String id) {
        InventoryFloatGlassShippedDetailPO po = inventoryFloatGlassShippedDetailDao.selectByPrimaryKey(id);
        return InventoryFloatGlassShippedDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_material_inventory_float_glass_shipped_detail")
    public List<InventoryFloatGlassShippedDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_material_inventory_float_glass_shipped_detail")
    public List<InventoryFloatGlassShippedDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryCriteriaParam)) {
            //过滤掉制造订单(全部同步要过滤掉子订单)
            queryCriteriaParam = queryCriteriaParam + " and actual_arrival_time is null";
        } else {
            queryCriteriaParam = " and actual_arrival_time is null";
        }
        List<InventoryFloatGlassShippedDetailVO> dataList = inventoryFloatGlassShippedDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryFloatGlassShippedDetailServiceImpl target = SpringBeanUtils.getBean(InventoryFloatGlassShippedDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryFloatGlassShippedDetailVO> selectByParams(Map<String, Object> params) {
        List<InventoryFloatGlassShippedDetailPO> list = inventoryFloatGlassShippedDetailDao.selectByParams(params);
        return InventoryFloatGlassShippedDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryFloatGlassShippedDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<InventoryFloatGlassShippedDetailVO> selectByPlanNumbersOrlineIds(Map<String, Object> params) {
        return InventoryFloatGlassShippedDetailConvertor.INSTANCE.po2Vos(inventoryFloatGlassShippedDetailDao.selectByPlanNumbersOrlineIds(params));
    }

    @Override
    public void doCreateBatchWithPartition(List<InventoryFloatGlassShippedDetailDTO> list, Integer pageSize) {
        List<InventoryFloatGlassShippedDetailPO> newList = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> inventoryFloatGlassShippedDetailDao.insertBatch(poList), pageSize);

    }

    @Override
    public void doUpdateBatchWithPartition(List<InventoryFloatGlassShippedDetailDTO> list, Integer pageSize) {
        List<InventoryFloatGlassShippedDetailPO> newList = InventoryFloatGlassShippedDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> inventoryFloatGlassShippedDetailDao.updateBatch(poList), pageSize);
    }

    @Override
    public BaseResponse<Void> syncOriginalFilmFFOnway(String tenantId) {
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(), tenantId);
        Map stockParams = MapUtil.builder().put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getData(), stockParams);
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        List<NewStockPointVO> saleStockPoints = newStockPointVOS.stream().filter(t ->
                Objects.equals(t.getOrganizeType(), StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())
                        && Objects.equals(t.getStockPointType(), StockPointTypeEnum.FF.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleStockPoints)) {
            return BaseResponse.error("浮法库存点信息为空");
        }
        List<NewStockPointVO> purchaseStockPoints = newStockPointVOS.stream().filter(t ->
                Objects.equals(t.getOrganizeType(), StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode())
                        && Objects.equals(t.getStockPointType(), StockPointTypeEnum.BC.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleStockPoints)) {
            return BaseResponse.error("本厂采购组织信息为空");
        }
        String customerCode = purchaseStockPoints.get(0).getCustomerCode();
        log.info("客户号为：{}", customerCode);
        Map<String, NewStockPointVO> stockPointMap = saleStockPoints.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        for (String stockPoint : stockPointMap.keySet()) {
            // 调用远程的销售组织信息
            Map<String, Object> newStockPoingMap = MapUtil.newHashMap(4);
            newStockPoingMap.put("stockPointCode", stockPoint);
            newStockPoingMap.put("orgId", stockPointMap.get(stockPoint).getOrganizeId());
            newStockPoingMap.put("customerNumber", customerCode);
            newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
            newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.ORIGINAL_FILM_FF_ONWAY.getCode(), newStockPoingMap);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> sync(String scenario, List<ErpOriginalFilmFFOnway> o) {
        if (CollectionUtils.isEmpty(o)) {
            log.error("接口返回原片浮法已发运库存批次明细数据为空");
            return BaseResponse.error("接口返回原片浮法已发运库存批次明细数据为空");
        }
        List<String> productCodes = o.stream().map(ErpOriginalFilmFFOnway::getItem).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectByProductCode(scenario, productCodes);
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            log.error("系统找不到物料信息");
            return BaseResponse.error("系统找不到物料信息");
        }
        Map<String, String> productStockPointVOMap =
                CollectionUtils.isEmpty(newProductStockPointVOS) ?
                        MapUtil.newHashMap() :
                        newProductStockPointVOS.stream().collect(
                                Collectors.toMap(t -> t.getStockPointCode() + "|" + t.getProductCode(),
                                        NewProductStockPointVO::getId, (v1, v2) -> v1));

        List<String> planNumbers = o.stream().map(ErpOriginalFilmFFOnway::getPlanNumber).distinct().collect(Collectors.toList());
        List<String> lineIds = o.stream().map(ErpOriginalFilmFFOnway::getLineId).distinct().collect(Collectors.toList());
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("planNumbers", planNumbers);
        params.put("lineIds", lineIds);
        List<InventoryFloatGlassShippedDetailPO> oldPOList = inventoryFloatGlassShippedDetailDao.selectByPlanNumbersOrlineIds(params);
        Map<String, InventoryFloatGlassShippedDetailPO> oldPOMap = CollectionUtils.isEmpty(oldPOList) ?
                MapUtil.newHashMap() :
                oldPOList.stream().collect(Collectors.toMap(t -> t.getPlanNumber() + "|" + t.getLineId() + "|" + t.getLotNumber(), Function.identity(), (v1, v2) -> v1));
        List<InventoryFloatGlassShippedDetailDTO> insetInventoryFloatGlassShippedDetailDTOS = Lists.newArrayList();
        List<InventoryFloatGlassShippedDetailDTO> updateInventoryFloatGlassShippedDetailDTOS = Lists.newArrayList();

        for (ErpOriginalFilmFFOnway erpOriginalFilmFFOnway : o) {
            InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO = new InventoryFloatGlassShippedDetailDTO();
            String dataKey = erpOriginalFilmFFOnway.getPlanNumber() + "|" + erpOriginalFilmFFOnway.getLineId() + "|" + erpOriginalFilmFFOnway.getLotNumber();
            String productCode = erpOriginalFilmFFOnway.getItem();
            String stockPointCode = erpOriginalFilmFFOnway.getOrgCode();
            String productDataKey = productCode + "|" + stockPointCode;
            if (!productStockPointVOMap.containsKey(productDataKey)) {
                log.error("没有找到对应的物料信息，接口物料编码：{}，接口库存点：{}", productCode, stockPointCode);
            }
            if (oldPOMap.containsKey(dataKey)) {
                log.info("原片浮法已发运库存批次明细数据匹配到已有数据：dataKey：{}", dataKey);
                InventoryFloatGlassShippedDetailPO oldPO = oldPOMap.get(dataKey);
                inventoryFloatGlassShippedDetailDTO=InventoryFloatGlassShippedDetailConvertor.INSTANCE.po2Dto(oldPO);
                inventoryFloatGlassShippedDetailDTO.setStockPointCode(erpOriginalFilmFFOnway.getOrgCode());
                inventoryFloatGlassShippedDetailDTO.setPlanNumber(erpOriginalFilmFFOnway.getPlanNumber());
                inventoryFloatGlassShippedDetailDTO.setLineId(erpOriginalFilmFFOnway.getLineId());
                inventoryFloatGlassShippedDetailDTO.setProductCode(erpOriginalFilmFFOnway.getItem());
                inventoryFloatGlassShippedDetailDTO.setProductSpec(erpOriginalFilmFFOnway.getSpecifications());
                if (Objects.isNull(inventoryFloatGlassShippedDetailDTO.getContainerNumber())) {
                    inventoryFloatGlassShippedDetailDTO.setContainerNumber(erpOriginalFilmFFOnway.getCabinetNo());
                }
                inventoryFloatGlassShippedDetailDTO.setDeliveryMethod(erpOriginalFilmFFOnway.getShipmentMethod());
                inventoryFloatGlassShippedDetailDTO.setDeliveryTime(erpOriginalFilmFFOnway.getShipDate());
                if (Objects.nonNull(erpOriginalFilmFFOnway.getPerBox())) {
                    inventoryFloatGlassShippedDetailDTO.setPerBox(new BigDecimal(erpOriginalFilmFFOnway.getPerBox()));
                }
                if (Objects.nonNull(erpOriginalFilmFFOnway.getBox())) {
                    inventoryFloatGlassShippedDetailDTO.setBox(new BigDecimal(erpOriginalFilmFFOnway.getBox()));
                }
                if (Objects.nonNull(erpOriginalFilmFFOnway.getArea())) {
                    inventoryFloatGlassShippedDetailDTO.setArea(new BigDecimal(erpOriginalFilmFFOnway.getArea()));
                }
                if (Objects.nonNull(erpOriginalFilmFFOnway.getWeight())) {
                    inventoryFloatGlassShippedDetailDTO.setWeight(new BigDecimal(erpOriginalFilmFFOnway.getWeight()));
                }
                if (productStockPointVOMap.containsKey(productDataKey)) {
                    String productId = productStockPointVOMap.get(productDataKey);
                    inventoryFloatGlassShippedDetailDTO.setProductId(productId);
                }
                inventoryFloatGlassShippedDetailDTO.setActualSentQuantity(new BigDecimal(erpOriginalFilmFFOnway.getQty()));
                inventoryFloatGlassShippedDetailDTO.setCarrier(erpOriginalFilmFFOnway.getCarriers());
                inventoryFloatGlassShippedDetailDTO.setLotNumber(erpOriginalFilmFFOnway.getLotNumber());
                inventoryFloatGlassShippedDetailDTO.setLevel(erpOriginalFilmFFOnway.getGrade());
                inventoryFloatGlassShippedDetailDTO.setLotLevelCode(erpOriginalFilmFFOnway.getGradeChange());
                if (Objects.nonNull(erpOriginalFilmFFOnway.getBillNo())) {
                    inventoryFloatGlassShippedDetailDTO.setAllocateNo(erpOriginalFilmFFOnway.getBillNo());
//                    inventoryFloatGlassShippedDetailDTO.setBillNo(erpOriginalFilmFFOnway.getBillNo());
                }
                inventoryFloatGlassShippedDetailDTO.setShipmentMethod(erpOriginalFilmFFOnway.getShipmentMethod());
                inventoryFloatGlassShippedDetailDTO.setRemark(erpOriginalFilmFFOnway.getRemark());
                inventoryFloatGlassShippedDetailDTO.setLastUpdateDate(erpOriginalFilmFFOnway.getLastUpdateDate());
                inventoryFloatGlassShippedDetailDTO.setStorageFlag(YesOrNoEnum.NO.getCode());
                inventoryFloatGlassShippedDetailDTO.setOrderPrice(new BigDecimal(erpOriginalFilmFFOnway.getOrderPrice()));
                updateInventoryFloatGlassShippedDetailDTOS.add(inventoryFloatGlassShippedDetailDTO);
            } else {
                log.info("原片浮法已发运库存批次明细数据没有匹配到已有数据：dataKey：{}", dataKey);
                inventoryFloatGlassShippedDetailDTO.setStockPointCode(erpOriginalFilmFFOnway.getOrgCode());
                inventoryFloatGlassShippedDetailDTO.setPlanNumber(erpOriginalFilmFFOnway.getPlanNumber());
                inventoryFloatGlassShippedDetailDTO.setLineId(erpOriginalFilmFFOnway.getLineId());
                inventoryFloatGlassShippedDetailDTO.setProductCode(erpOriginalFilmFFOnway.getItem());
                inventoryFloatGlassShippedDetailDTO.setProductSpec(erpOriginalFilmFFOnway.getSpecifications());
                inventoryFloatGlassShippedDetailDTO.setDeliveryMethod(erpOriginalFilmFFOnway.getShipmentMethod());
                inventoryFloatGlassShippedDetailDTO.setDeliveryTime(erpOriginalFilmFFOnway.getShipDate());
                if (Objects.nonNull(erpOriginalFilmFFOnway.getBillNo())) {
                    inventoryFloatGlassShippedDetailDTO.setAllocateNo(erpOriginalFilmFFOnway.getBillNo());
//                    inventoryFloatGlassShippedDetailDTO.setBillNo(erpOriginalFilmFFOnway.getBillNo());
                }
                inventoryFloatGlassShippedDetailDTO.setContainerNumber(erpOriginalFilmFFOnway.getCabinetNo());
                inventoryFloatGlassShippedDetailDTO.setRemark(erpOriginalFilmFFOnway.getRemark());
                if (Objects.nonNull(erpOriginalFilmFFOnway.getPerBox())) {
                    inventoryFloatGlassShippedDetailDTO.setPerBox(new BigDecimal(erpOriginalFilmFFOnway.getPerBox()));
                }
                if (Objects.nonNull(erpOriginalFilmFFOnway.getBox())) {
                    inventoryFloatGlassShippedDetailDTO.setBox(new BigDecimal(erpOriginalFilmFFOnway.getBox()));
                }
                if (Objects.nonNull(erpOriginalFilmFFOnway.getArea())) {
                    inventoryFloatGlassShippedDetailDTO.setArea(new BigDecimal(erpOriginalFilmFFOnway.getArea()));
                }
                if (Objects.nonNull(erpOriginalFilmFFOnway.getWeight())) {
                    inventoryFloatGlassShippedDetailDTO.setWeight(new BigDecimal(erpOriginalFilmFFOnway.getWeight()));
                }
                if (productStockPointVOMap.containsKey(productDataKey)) {
                    String productId = productStockPointVOMap.get(productDataKey);
                    inventoryFloatGlassShippedDetailDTO.setProductId(productId);
                }
                inventoryFloatGlassShippedDetailDTO.setCarrier(erpOriginalFilmFFOnway.getCarriers());
                inventoryFloatGlassShippedDetailDTO.setLotNumber(erpOriginalFilmFFOnway.getLotNumber());
                inventoryFloatGlassShippedDetailDTO.setLevel(erpOriginalFilmFFOnway.getGrade());
                inventoryFloatGlassShippedDetailDTO.setLotLevelCode(erpOriginalFilmFFOnway.getGradeChange());
                inventoryFloatGlassShippedDetailDTO.setShipmentMethod(erpOriginalFilmFFOnway.getShipmentMethod());
                inventoryFloatGlassShippedDetailDTO.setActualSentQuantity(new BigDecimal(erpOriginalFilmFFOnway.getQty()));
                inventoryFloatGlassShippedDetailDTO.setLastUpdateDate(erpOriginalFilmFFOnway.getLastUpdateDate());
                inventoryFloatGlassShippedDetailDTO.setStorageFlag(YesOrNoEnum.NO.getCode());
                inventoryFloatGlassShippedDetailDTO.setOrderPrice(new BigDecimal(erpOriginalFilmFFOnway.getOrderPrice()));
                insetInventoryFloatGlassShippedDetailDTOS.add(inventoryFloatGlassShippedDetailDTO);
            }

        }
        if (CollectionUtils.isNotEmpty(insetInventoryFloatGlassShippedDetailDTOS)) {
            this.doCreateBatchWithPartition(insetInventoryFloatGlassShippedDetailDTOS, 5000);
            log.info("新增原片浮法已发运库存批次明细数据条数：{}", insetInventoryFloatGlassShippedDetailDTOS.size());
        }
        if (CollectionUtils.isNotEmpty(updateInventoryFloatGlassShippedDetailDTOS)) {
            this.doUpdateBatchWithPartition(updateInventoryFloatGlassShippedDetailDTOS, 5000);
            log.info("更新原片浮法已发运库存批次明细数据条数：{}", updateInventoryFloatGlassShippedDetailDTOS.size());
        }

//        CompletableFuture<Void> glassMrpFuture = CompletableFuture.runAsync(() -> {
//            try {
//                noGlassMrpService.doRunRecalculateMrp(SystemHolder.getScenario(), null);
//            } catch (Exception e) {
//                log.error("MRP材料推移失败", e);
//            }
//        });
//        CompletableFuture<Void> allFutures = CompletableFuture.allOf(glassMrpFuture);
//        allFutures.join();
        return BaseResponse.success("同步成功");
    }

    private void shippedDetailEnabled(List<InventoryFloatGlassShippedDetailPO> inventoryFloatGlassShippedDetailPOS) {

        List<InventoryFloatGlassShippedDetailPO> shippedDetailVOList = inventoryFloatGlassShippedDetailPOS.stream()
                .filter(po -> po.getPo() != null
                        && YesOrNoEnum.YES.getCode().equals(po.getStorageFlag())
                        && "汽车运输".equals(po.getDeliveryMethod()))
                .collect(Collectors.toList());
        shippedDetailVOList.forEach(detailVO -> detailVO.setEnabled(YesOrNoEnum.NO.getCode()));
        if (CollectionUtils.isNotEmpty(shippedDetailVOList)) {
            BasePOUtils.updateBatchFiller(shippedDetailVOList);
            inventoryFloatGlassShippedDetailDao.updateBatch(shippedDetailVOList);
        }
    }

    private void inventoryShippedQuayDetail(List<InventoryFloatGlassShippedDetailPO> inventoryFloatGlassShippedDetailPOList) {
        List<InventoryFloatGlassShippedDetailPO> oceanFreightList =
                inventoryFloatGlassShippedDetailPOList.stream()
                        .filter(po -> po.getActualArrivalTime() != null)
                        .filter(po -> "出口海运".equals(po.getShipmentMethod()) || "国内船运".equals(po.getShipmentMethod()) || "集装箱直运".equals(po.getShipmentMethod()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(oceanFreightList)) {
            List<InventoryQuayDetailPO> inventoryQuayDetailPOS = inventoryQuayDetailsDao.selectByInventoryQuayDetailFloatGlassShippedDetail(oceanFreightList);
            HashMap<String, Object> freeStorageMap = MapUtil.newHashMap(3);
            freeStorageMap.put("enabled", YesOrNoEnum.YES.getCode());
            List<ProFreeStorageVO> proFreeStorageVOS = proFreeStorageService.selectByParams(freeStorageMap);
            Map<String, ProFreeStorageVO> proFreeStorageVOMap = CollectionUtils.isEmpty(proFreeStorageVOS) ?
                    MapUtil.newHashMap() :
                    proFreeStorageVOS.stream().collect(Collectors.toMap(t -> t.getPortName() + "_" + t.getCarrierCode(),
                            Function.identity(), (v1, v2) -> v1));
            Map<String, InventoryQuayDetailPO> inventoryQuayDetailMap = CollectionUtils.isEmpty(inventoryQuayDetailPOS) ?
                    MapUtil.newHashMap() :
                    inventoryQuayDetailPOS.stream().collect(Collectors.toMap(t -> t.getLotNumber() + "_" + t.getPlanNumber() + "_" + t.getLineId(),
                            Function.identity(), (v1, v2) -> v1));
            List<InventoryQuayDetailDTO> insertQuayDetailDtoS = new ArrayList<>();
            List<InventoryQuayDetailDTO> updateQuayDetailDtoS = new ArrayList<>();
            oceanFreightList.forEach(detailVO -> {
                detailVO.setEnabled(YesOrNoEnum.NO.getCode());
                detailVO.setStorageFlag(YesOrNoEnum.NO.getCode());
            });
            Calendar calendar = Calendar.getInstance();
            for (InventoryFloatGlassShippedDetailPO oceanFreight : oceanFreightList) {
                Date overdueTime = null;
                String freeId = oceanFreight.getPortName() + "_" + oceanFreight.getCarrier();
                if (proFreeStorageVOMap.containsKey(freeId)) {
                    ProFreeStorageVO proFreeStorageVO = proFreeStorageVOMap.get(freeId);
                    int freeStorageDays = Integer.parseInt(proFreeStorageVO.getFreeStorage());

                    calendar.setTime(oceanFreight.getActualArrivalTime());
                    // 加上 freeStorage 天数，再减去 1 天
                    calendar.add(Calendar.DAY_OF_MONTH, freeStorageDays - 1);
                    overdueTime = calendar.getTime();
                }
                InventoryQuayDetailDTO inventoryQuayDetail = InventoryQuayDetailDTO.builder()
                        .productId(oceanFreight.getProductId())
                        .productCode(oceanFreight.getProductCode())
                        .productSpec(oceanFreight.getProductSpec())
                        .stockPointCode(oceanFreight.getStockPointCode())
                        .level(oceanFreight.getLevel())
                        .lotLevelCode(oceanFreight.getLotLevelCode())
                        .lotNumber(oceanFreight.getLotNumber())
                        .perBox(oceanFreight.getPerBox())
                        .box(oceanFreight.getBox())
                        .actualSentQuantity(oceanFreight.getActualSentQuantity())
                        .area(oceanFreight.getArea())
                        .weight(oceanFreight.getWeight())
                        .packageType(oceanFreight.getPackageType())
                        .po(oceanFreight.getPo())
                        .containerNumber(oceanFreight.getContainerNumber())
                        .actualArrivalTime(oceanFreight.getActualArrivalTime())
                        .overdueTime(overdueTime)
                        .portName(oceanFreight.getPortName())
                        .carrier(oceanFreight.getCarrier())
                        .cuttingRate(oceanFreight.getCuttingRate())
                        .containerDeliveryTime(oceanFreight.getContainerDeliveryTime())
                        .planNumber(oceanFreight.getPlanNumber())
                        .lineId(oceanFreight.getLineId())
                        .manufacturer(oceanFreight.getManufacturer())
                        .orderPrice(oceanFreight.getOrderPrice())
                        .poNumber(oceanFreight.getPoNumber())
                        .billNo(oceanFreight.getBillNo())
                        .deliveryTime(oceanFreight.getDeliveryTime())
                        .storageFlag(oceanFreight.getStorageFlag())
                        .build();
                String id = inventoryQuayDetail.getLotNumber() + "_" + inventoryQuayDetail.getPlanNumber() + "_" + inventoryQuayDetail.getLineId();
                if (inventoryQuayDetailMap.containsKey(id)) {
                    InventoryQuayDetailPO inventoryQuayDetailPO = inventoryQuayDetailMap.get(id);
                    inventoryQuayDetail.setId(inventoryQuayDetailPO.getId());
                    updateQuayDetailDtoS.add(inventoryQuayDetail);
                } else {
                    insertQuayDetailDtoS.add(inventoryQuayDetail);
                }
            }
            log.info("外购插入数据:{}，外购更新数据:{}", insertQuayDetailDtoS, updateQuayDetailDtoS);
            if (CollectionUtils.isNotEmpty(insertQuayDetailDtoS)) {
                inventoryQuayDetailService.doCreateBatch(insertQuayDetailDtoS);
            }
            if (CollectionUtils.isNotEmpty(updateQuayDetailDtoS)) {
                inventoryQuayDetailService.doUpdateBatch(updateQuayDetailDtoS);
            }
            BasePOUtils.updateBatchFiller(oceanFreightList);
            inventoryFloatGlassShippedDetailDao.updateBatch(oceanFreightList);
        }
    }

    @Override
    public void doUpload(MultipartFile file) {
        List<InventoryFloatGlassShippedDetailExcelDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(InventoryFloatGlassShippedDetailExcelDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(fileList)) {
            throw new BusinessException("文件数据为空");
        }

        // 过滤掉主键为空的数据
        fileList = fileList.stream().filter(data -> StringUtils.isNotEmpty(data.getLotNumber())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileList)) {
            throw new BusinessException("批次号不能为空");
        }

        List<String> lotNumbers = fileList.stream().map(InventoryFloatGlassShippedDetailExcelDTO::getLotNumber).distinct().collect(Collectors.toList());

        // 获取当前浮法已发运数据（批次号不能为空）
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOList = this.selectByParams(ImmutableMap.of("lotNumbers", lotNumbers)).stream()
                .filter(data -> StringUtils.isNotEmpty(data.getLotNumber()))
                .collect(Collectors.toList());
        // 根据批次号分组
        Map<String, InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailMap = inventoryFloatGlassShippedDetailVOList.stream()
                .collect(Collectors.toMap(InventoryFloatGlassShippedDetailVO::getLotNumber, Function.identity(), (v1, v2) -> v1));

        // 获取当前码头数据（批次号不能为空）
        List<InventoryQuayDetailVO> inventoryQuayDetailVOList = inventoryQuayDetailService.selectByParams(ImmutableMap.of("lotNumbers", lotNumbers)).stream()
                .filter(data -> StringUtils.isNotEmpty(data.getLotNumber()))
                .collect(Collectors.toList());
        // 根据批次号分组
        Map<String, InventoryQuayDetailVO> inventoryQuayDetailVOMap = inventoryQuayDetailVOList.stream()
                .collect(Collectors.toMap(InventoryQuayDetailVO::getLotNumber, Function.identity(), (v1, v2) -> v1));

        // 新增浮法已发运
        List<InventoryFloatGlassShippedDetailDTO> addFloatGlassShippedDetailist = new ArrayList<>();
        // 修改浮法已发运
        List<InventoryFloatGlassShippedDetailDTO> updateFloatGlassShippedDetailist = new ArrayList<>();

        // 新增码头
        List<InventoryQuayDetailDTO> addQuayDetailList = new ArrayList<>();
        // 修改码头
        List<InventoryQuayDetailDTO> updateQuayDetailList = new ArrayList<>();

        // 更新浮法已发运
        for (InventoryFloatGlassShippedDetailExcelDTO inventoryFloatGlassShippedDetailExcelDTO : fileList) {
            String key = inventoryFloatGlassShippedDetailExcelDTO.getLotNumber();

            // 获取对应浮法已发运数据
            InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO = inventoryFloatGlassShippedDetailMap.get(key);
            if (Objects.isNull(inventoryFloatGlassShippedDetailVO)) {
                // 添加浮法已发运数据
                InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO = new InventoryFloatGlassShippedDetailDTO();
                BeanUtils.copyProperties(inventoryFloatGlassShippedDetailExcelDTO, inventoryFloatGlassShippedDetailDTO);
                addFloatGlassShippedDetailist.add(inventoryFloatGlassShippedDetailDTO);
            } else {
                // 修改浮法已发运数据
                InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO = new InventoryFloatGlassShippedDetailDTO();
                BeanUtils.copyProperties(inventoryFloatGlassShippedDetailExcelDTO, inventoryFloatGlassShippedDetailDTO);
                inventoryFloatGlassShippedDetailDTO.setId(inventoryFloatGlassShippedDetailVO.getId());
                updateFloatGlassShippedDetailist.add(inventoryFloatGlassShippedDetailDTO);
            }

            // 获取对应码头数据
            InventoryQuayDetailVO inventoryQuayDetailVO = inventoryQuayDetailVOMap.get(key);
            if (Objects.isNull(inventoryQuayDetailVO)) {
                // 添加码头数据
                InventoryQuayDetailDTO inventoryQuayDetailDTO = new InventoryQuayDetailDTO();
                BeanUtils.copyProperties(inventoryFloatGlassShippedDetailExcelDTO, inventoryQuayDetailDTO);
                addQuayDetailList.add(inventoryQuayDetailDTO);
            } else {
                // 修改码头数据
                InventoryQuayDetailDTO inventoryQuayDetailDTO = new InventoryQuayDetailDTO();
                BeanUtils.copyProperties(inventoryFloatGlassShippedDetailExcelDTO, inventoryQuayDetailDTO);
                inventoryQuayDetailDTO.setId(inventoryQuayDetailVO.getId());
                updateQuayDetailList.add(inventoryQuayDetailDTO);
            }
        }

        // 添加浮法已发运
        if (CollectionUtils.isNotEmpty(addFloatGlassShippedDetailist)) {
            Lists.partition(addFloatGlassShippedDetailist, 500).forEach(this::doCreateBatch);
        }

        // 修改浮法已发运（固定字段）
        if (CollectionUtils.isNotEmpty(updateFloatGlassShippedDetailist)) {
            Lists.partition(updateFloatGlassShippedDetailist, 500).forEach(this::doUpdateBatchSelective);
        }

        // 添加码头数据
        if (CollectionUtils.isNotEmpty(addQuayDetailList)) {
            Lists.partition(addQuayDetailList, 500).forEach(inventoryQuayDetailService::doCreateBatch);
        }

        // 修改码头数据
        if (CollectionUtils.isNotEmpty(updateQuayDetailList)) {
            Lists.partition(updateQuayDetailList, 500).forEach(inventoryQuayDetailService::doUpdateBatchSelective);
        }
    }

    @SneakyThrows
    @Override
    public void exportTemplateInternal(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "浮法已发运（内部）导入模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("柜号*"));
        headers.add(Collections.singletonList("承运商*"));
        headers.add(Collections.singletonList("提单号"));
        headers.add(Collections.singletonList("正确柜号"));
        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public BaseResponse<String> doUploadInternal(MultipartFile file) {
        Set<String> errorMegList = new HashSet<>();
        List<InventoryFloatGlassShippedDetailInternalExcelDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(InventoryFloatGlassShippedDetailInternalExcelDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(fileList)) throw new BusinessException("文件数据为空");

        for (InventoryFloatGlassShippedDetailInternalExcelDTO excelDTO : fileList) {
            if (StringUtils.isEmpty(excelDTO.getContainerNumber())) throw new BusinessException("柜号不能为空");
            if (StringUtils.isEmpty(excelDTO.getCarrier())) throw new BusinessException("承运商不能为空");
//            if (StringUtils.isEmpty(excelDTO.getBillNo())) throw new BusinessException("提单号不能为空");
        }
        // 过滤出正确柜号不为空的数据
        List<InventoryFloatGlassShippedDetailInternalExcelDTO> correctContainerNumberList = fileList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getCorrectContainerNumber()))
                .collect(Collectors.toList());

        // 更新柜号数据
        if (CollectionUtils.isNotEmpty(correctContainerNumberList)) {
            updateContainerNumber(fileList, correctContainerNumberList);
        }

        // 根据柜号 + 承运商 分组
        Map<String, InventoryFloatGlassShippedDetailInternalExcelDTO> fileListMap = fileList.stream()
                .collect(Collectors.toMap(data ->
                                String.join("_", data.getContainerNumber(), data.getCarrier()),
                        Function.identity(), (v1, v2) -> v1));
        // 收集柜号 + 承运商
        List<String> combineKeys = fileList.stream()
                .map(data -> String.join("_", data.getContainerNumber(), data.getCarrier()))
                .distinct()
                .collect(Collectors.toList());

        // 根据唯一键（柜号 + 承运商） 获取当前浮法已发运数据
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOList =
                this.selectByParams(ImmutableMap.of("combineKeys", combineKeys));

        if (CollectionUtils.isEmpty(inventoryFloatGlassShippedDetailVOList)) {
            return BaseResponse.success(null, "无可用浮法已发运数据");
        }
        Map<String, List<InventoryFloatGlassShippedDetailVO>> dataBaseDataGroup = inventoryFloatGlassShippedDetailVOList.stream()
                .collect(Collectors.groupingBy(data -> String.join("_", data.getContainerNumber(), data.getCarrier())));

        List<InventoryFloatGlassShippedDetailDTO> updateList = new ArrayList<>();

        // 根据 柜号 + 承运商 分组 ，修改 提单号
        for (Map.Entry<String, List<InventoryFloatGlassShippedDetailVO>> entry : dataBaseDataGroup.entrySet()) {
            // 获取file中的对应的数据
            InventoryFloatGlassShippedDetailInternalExcelDTO excelDTO = fileListMap.get(entry.getKey());

            // 修改 提单号
            for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : entry.getValue()) {
                updateList.add(InventoryFloatGlassShippedDetailDTO.builder()
                        .id(inventoryFloatGlassShippedDetailVO.getId())
                        .billNo(excelDTO.getBillNo())
                        .build());
            }
        }
        Lists.partition(updateList, 500).forEach(this::doUpdateBatchSelective);

        Set<String> dataBaseKeys = dataBaseDataGroup.keySet();
        Set<String> difference = combineKeys.stream()
                .filter(key -> !dataBaseKeys.contains(key))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(difference)) {
            // difference集合按照下划线分割获取0索引的数据
            List<String> differenceList = difference.stream()
                    .map(key -> key.split("_"))
                    .map(key -> key[0])
                    .collect(Collectors.toList());
            String errorMessage = "%s没有匹配到提单号";
            errorMegList.add(String.format(errorMessage, differenceList));
            return BaseResponse.success("", String.join(",", errorMegList));
        }
        return BaseResponse.success();
    }

    private void updateContainerNumber(List<InventoryFloatGlassShippedDetailInternalExcelDTO> fileList,
                                       List<InventoryFloatGlassShippedDetailInternalExcelDTO> correctContainerNumberList) {
        if (CollectionUtils.isEmpty(correctContainerNumberList)) {
            return;
        }

        // 收集柜号 + 承运商
        List<String> combineKeys = fileList.stream()
                .map(data -> String.join("_", data.getContainerNumber(), data.getCarrier()))
                .distinct()
                .collect(Collectors.toList());

        // 根据唯一键（柜号 + 承运商） 获取当前浮法已发运数据
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOList =
                this.selectByParams(ImmutableMap.of("combineKeys", combineKeys,
                        "enabled", YesOrNoEnum.YES.getCode()));

        if (CollectionUtils.isEmpty(inventoryFloatGlassShippedDetailVOList)) {
            return;
        }

        Map<String, List<InventoryFloatGlassShippedDetailVO>> floatGlassShippedDetailVOMap = inventoryFloatGlassShippedDetailVOList.stream()
                .collect(Collectors.groupingBy(data ->
                        String.join("_", data.getContainerNumber(), data.getCarrier())));

        List<InventoryFloatGlassShippedDetailPO> updateContainerNumberList = new ArrayList<>();
        for (InventoryFloatGlassShippedDetailInternalExcelDTO excelDTO : correctContainerNumberList) {
            String joinKey = String.join("_", excelDTO.getContainerNumber(), excelDTO.getCarrier());
            if (floatGlassShippedDetailVOMap.containsKey(joinKey)) {
                List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS = floatGlassShippedDetailVOMap.get(joinKey);
                inventoryFloatGlassShippedDetailVOS.forEach(item -> item.setContainerNumber(excelDTO.getCorrectContainerNumber()));
                // 更新为正确柜号
                List<InventoryFloatGlassShippedDetailPO> inventoryFloatGlassShippedDetailPOList = InventoryFloatGlassShippedDetailConvertor.INSTANCE.vo2Pos(inventoryFloatGlassShippedDetailVOS);
                updateContainerNumberList.addAll(inventoryFloatGlassShippedDetailPOList);
            }
        }
        if (CollectionUtils.isNotEmpty(updateContainerNumberList)) {
            Lists.partition(updateContainerNumberList, 500).forEach(inventoryFloatGlassShippedDetailDao::updateBatchSelective);
        }
        // 更新掉fileList里面的柜号
        for (InventoryFloatGlassShippedDetailInternalExcelDTO excelDTO : fileList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(excelDTO.getCorrectContainerNumber())) {
                excelDTO.setContainerNumber(excelDTO.getCorrectContainerNumber());
            }
        }
    }

    @SneakyThrows
    @Override
    public void exportTemplateOutsourcing(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "浮法已发运（外购）导入模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("规格"));
        headers.add(Collections.singletonList("等级"));
//        headers.add(Collections.singletonList("批次等级代码"));
        headers.add(Collections.singletonList("批次号"));
        headers.add(Collections.singletonList("片/箱*"));
        headers.add(Collections.singletonList("箱数*"));
        headers.add(Collections.singletonList("实发片数"));
        headers.add(Collections.singletonList("面积"));
        headers.add(Collections.singletonList("吨数"));
        headers.add(Collections.singletonList("包装方式"));
        headers.add(Collections.singletonList("发运方式*"));
//        headers.add(Collections.singletonList("发货方式*"));
//        headers.add(Collections.singletonList("PO"));
        headers.add(Collections.singletonList("柜号*"));
        headers.add(Collections.singletonList("预计到港时间"));
        headers.add(Collections.singletonList("实际到港时间"));
//        headers.add(Collections.singletonList("送柜时间"));
        headers.add(Collections.singletonList("港口"));
        headers.add(Collections.singletonList("承运商*"));
//        headers.add(Collections.singletonList("要求到港时间"));
        headers.add(Collections.singletonList("发货时间"));
//        headers.add(Collections.singletonList("厂家"));
        headers.add(Collections.singletonList("物品编码*"));
        headers.add(Collections.singletonList("厂家*"));
        headers.add(Collections.singletonList("提单号"));
        headers.add(Collections.singletonList("调拨单号"));
        headers.add(Collections.singletonList("备注"));

        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public void uploadOutsourcing(MultipartFile file) {

        List<InventoryFloatGlassShippedDetailOutsourcingExcelDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(InventoryFloatGlassShippedDetailOutsourcingExcelDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(fileList)) throw new BusinessException("文件数据为空");

        List<String> shipmentMethodList = Lists.newArrayList("集装箱直运", "汽车运输");

        for (InventoryFloatGlassShippedDetailOutsourcingExcelDTO excelDTO : fileList) {
            if (StringUtils.isEmpty(excelDTO.getContainerNumber())) throw new BusinessException("柜号*不能为空");
            if (StringUtils.isEmpty(excelDTO.getCarrier())) throw new BusinessException("承运商*不能为空");
            if (null == excelDTO.getPerBox()) throw new BusinessException("片/箱*不能为空");
            if (null == excelDTO.getBox()) throw new BusinessException("箱数*不能为空");
            if (StringUtils.isEmpty(excelDTO.getShipmentMethod())) throw new BusinessException("发运方式*不能为空");
            if (StringUtils.isEmpty(excelDTO.getProductCode())) throw new BusinessException("物品编码*不能为空");
            if (StringUtils.isEmpty(excelDTO.getStockPointCode())) throw new BusinessException("厂家*不能为空");
//            if (StringUtils.isEmpty(excelDTO.getLotNumber())) throw new BusinessException("批次号不能为空");
            // 校验发运方式
            if (!shipmentMethodList.contains(excelDTO.getShipmentMethod())) {
                throw new BusinessException("发运方式只能为 集装箱直运或汽车运输");
            }
        }

        // 物料编码 + 柜号 + 承运商
        List<String> combineKeys03 = fileList.stream()
                .map(data -> String.join("_", data.getProductCode(), data.getContainerNumber(), data.getCarrier()))
                .distinct()
                .collect(Collectors.toList());

        // 根据唯一键（物料编码 + 柜号 + 承运商） 获取当前浮法已发运数据
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOList =
                this.selectByParams(ImmutableMap.of("combineKeys03", combineKeys03));

        // 根据物料编码 + 柜号 + 承运商 分组
        Map<String, InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOMap = inventoryFloatGlassShippedDetailVOList.stream()
                .collect(Collectors.toMap(data -> String.join("_", data.getProductCode(), data.getContainerNumber(), data.getCarrier()),
                        Function.identity(), (v1, v2) -> v1));

        List<InventoryFloatGlassShippedDetailDTO> addList = new ArrayList<>();
        List<InventoryFloatGlassShippedDetailDTO> updateList = new ArrayList<>();

        for (InventoryFloatGlassShippedDetailOutsourcingExcelDTO excelDTO : fileList) {
            String key = String.join("_", excelDTO.getProductCode(), excelDTO.getContainerNumber(), excelDTO.getCarrier());

            InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO = inventoryFloatGlassShippedDetailVOMap.get(key);

            InventoryFloatGlassShippedDetailDTO dto = new InventoryFloatGlassShippedDetailDTO();

            if (Objects.isNull(inventoryFloatGlassShippedDetailVO)) {
                BeanUtils.copyProperties(excelDTO, dto);
                addList.add(dto);
            } else {
                BeanUtils.copyProperties(excelDTO, dto);
                dto.setId(inventoryFloatGlassShippedDetailVO.getId());
                updateList.add(dto);
            }
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            Lists.partition(updateList, 500).forEach(this::doUpdateBatchSelective);
        }
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("enabled", YesOrNoEnum.YES.getCode());
        List<InventoryFloatGlassShippedDetailPO> inventoryFloatGlassShippedDetailPOS =
                inventoryFloatGlassShippedDetailDao.selectByParams(map);
        log.info("外购导入开始");
        shippedDetailEnabled(inventoryFloatGlassShippedDetailPOS);
        inventoryShippedQuayDetail(inventoryFloatGlassShippedDetailPOS);
        log.info("外购导入结束");
    }

    @SneakyThrows
    @Override
    public void downloadErrorData(HttpServletResponse response) {
        List<InventoryFloatGlassShippedDetailVailidateExcelDTO> errorList = Lists.newArrayList();
        List<InventoryFloatGlassShippedDetailValidateVO> shippedDetailVOS = inventoryFloatGlassShippedDetailDao.selectErrorStockPoint();


        shippedDetailVOS.forEach(t -> {
            InventoryFloatGlassShippedDetailVailidateExcelDTO inventoryFloatGlassShippedDetailVailidateExcelDTO
                    = new InventoryFloatGlassShippedDetailVailidateExcelDTO();
            inventoryFloatGlassShippedDetailVailidateExcelDTO.setProductCode(t.getProductCode());
            inventoryFloatGlassShippedDetailVailidateExcelDTO.setStockPointCode(t.getStockPointCode());
            inventoryFloatGlassShippedDetailVailidateExcelDTO.setRequireStockPointCode(t.getRequireStockPointCode());
            errorList.add(inventoryFloatGlassShippedDetailVailidateExcelDTO);
        });
        String fileName = URLEncoder.encode(ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL.getDesc()
                + "校验错误数据", "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), InventoryFloatGlassShippedDetailVailidateExcelDTO.class)
                .sheet(ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL.getDesc()).doWrite(errorList);
    }

    @Override
    public BaseResponse<Void> handlePoCreate(String scenario, List<ErpPoCreate> o) {
        if (CollectionUtils.isEmpty(o)) {
            log.error("接口返回PO创建为空");
            return BaseResponse.error("接口返回PO创建为空");
        }
        List<String> ids = o.stream()
                .map(item -> item.getBpimId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, ErpPoCreate> erpPoCreateMap = o.stream()
                .collect(Collectors.toMap(ErpPoCreate::getBpimId, Function.identity(),
                        (value1, value2) -> value1));
        List<InventoryFloatGlassShippedDetailPO> chooseIssueList =
                inventoryFloatGlassShippedDetailDao.selectByParams(MapUtil.of("ids", ids));
        chooseIssueList.forEach(e -> {
            ErpPoCreate erpPoCreate = erpPoCreateMap.get(e.getId());
            if (erpPoCreate != null) {
                e.setPo(erpPoCreate.getPoNo());
                e.setPoNumber(erpPoCreate.getLines().get(0).getLineNum());
            }
        });
        BasePOUtils.updateBatchFiller(chooseIssueList);
        inventoryFloatGlassShippedDetailDao.updateBatch(chooseIssueList);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> syncPoCreate(String tenantId) {
        Map<String, Object> map = MapUtil.newHashMap(3);
        map.put("type", ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL.getCode());
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PO_CREATE.getCode(), map);
        return BaseResponse.success("同步成功");
    }

    @Override
    public String syncAutoCreatPo(String tenantId) {
        if (Boolean.TRUE.equals(redisUtil.hasKey(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE.getKey()))) {
            throw new BusinessException("浮法已发运正在创建PO，请稍后操作");
        }
        Map<String, Object> map = MapUtil.newHashMap(3);
        List<InventoryFloatGlassShippedDetailVO> chooseIssueList =
                inventoryFloatGlassShippedDetailDao.selectVOByParams(map);
        chooseIssueList = chooseIssueList.stream()
                .filter(item -> item.getPo() == null && "汽车运输".equals(item.getShipmentMethod()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chooseIssueList)) {
            throw new BusinessException("已发运数据需要同时满足不存在PO且是汽车运输才能创建PO");
        }

        return methodCreatPo(chooseIssueList);
    }

    @Override
    public String doBatchCreatPo(List<String> ids) {
        if (Boolean.TRUE.equals(redisUtil.hasKey(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE.getKey()))) {
            throw new BusinessException("浮法已发运正在创建PO，请稍后操作");
        }
        List<InventoryFloatGlassShippedDetailVO> chooseIssueList =
                inventoryFloatGlassShippedDetailDao.selectVOByParams(MapUtil.of("ids", ids));
        List<InventoryFloatGlassShippedDetailVO> chooseIssueErrorList = chooseIssueList.stream()
                .filter(item -> item.getPo() != null || !"汽车运输".equals(item.getShipmentMethod()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chooseIssueErrorList)) {
            throw new BusinessException("勾选的已发运数据需要同时满足不存在PO且是汽车运输才能创建PO");
        }

        return methodCreatPo(chooseIssueList);
    }

    private String methodCreatPo(List<InventoryFloatGlassShippedDetailVO> chooseIssueList) {
        // 锁定5分钟
        List<String> unIssuedIds = chooseIssueList.stream().map(InventoryFloatGlassShippedDetailVO::getId)
                .collect(Collectors.toList());
        log.info("当前id{}", JSONObject.toJSONString(unIssuedIds));
        redisUtil.set(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE.getKey(), String.join(",", unIssuedIds), 300);
        log.info("redis当前值{}", redisUtil.get(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE.getKey()).toString());
        redisUtil.set(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_USER.getKey(), SystemHolder.getUserId(), 300);
        try {
            // 已发运创建PO数据
            List<ErpPoCreate> erpPoCreateList = new ArrayList<>();
            initErpPoCreateList(chooseIssueList, erpPoCreateList);
            String filteredListJson = JSON.toJSONString(erpPoCreateList);
            // 已发运创建PO数据ERP
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("type", ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL.getCode());
            params.put("createList", filteredListJson);
            BaseResponse<String> baseResponse = newDcpFeign.callExternalApiReturn(SystemHolder.getTenantCode(),
                    ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.PO_CREATE.getCode(), params);
            log.info("po创建响应：" + JSONObject.toJSONString(baseResponse));
            // 结果解析
            if (Boolean.TRUE.equals(baseResponse.getSuccess()) && baseResponse.getData() != null) {
                // 创建PR成功后，更新状态
                String data = baseResponse.getData();
                log.info("po创建,获取接口日志请求:{}", data);
                ExtApiLogVO extApiLog = newDcpFeign.getExtApiLog(data);
                log.info("po创建,获取接口日志响应:{}", JSONObject.toJSONString(extApiLog));
                Integer resolveCount = extApiLog.getResolveCount();
                Integer applyCount = extApiLog.getApplyCount();
                String remark = extApiLog.getRemark();
                if (StringUtils.isEmpty(remark)) {
                    remark = "";
                }
                log.info("po创建, applyCount数据值：{}", applyCount);
                log.info("po创建, remark数据值：{}", remark);
                String result = "共%d条数据，成功%d条，失败%d条";
                result = String.format(result, resolveCount, applyCount, resolveCount - (applyCount == null ? 0 : applyCount));
                if (applyCount != resolveCount) {
                    result = result + remark;
                }
                return result + remark;
            } else {
                log.error("po创建下发报错，请查看最新对外接口日志");
                throw new BusinessException("po创建下发错误:" + baseResponse.getMsg());
            }
        } catch (Exception e) {
            log.error("po创建下发报错", e);
            throw new BusinessException("po创建下发报错：" + e.getMessage());
        } finally {
            redisUtil.delete(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_ISSUE.getKey());
            redisUtil.delete(RedisKeyManageEnum.INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL_USER.getKey());

        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL.getCode();
    }

    @Override
    public List<InventoryFloatGlassShippedDetailVO> invocation(List<InventoryFloatGlassShippedDetailVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    private void initErpPoCreateList(List<InventoryFloatGlassShippedDetailVO> chooseIssueList,
                                     List<ErpPoCreate> erpPoCreateList) {

        List<String> stockPointCodes = chooseIssueList.stream().map(InventoryFloatGlassShippedDetailVO::getStockPointCode)
                .distinct().collect(Collectors.toList());
        List<String> productCodes = chooseIssueList.stream()
                .map(item -> item.getProductCode())
                .distinct()
                .collect(Collectors.toList());
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> stockPointList = newMdsFeign.selectStockPointByParams(scenario.getData(),
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "stockPointCodes", stockPointCodes));
        if (CollectionUtils.isEmpty(stockPointList)) {
            throw new BusinessException("请先维护" + stockPointCodes.toString() + "各自的供应商");
        }
        List<String> supplierIds = stockPointList.stream()
                .filter(item -> item.getSupplierId() != null)
                .map(item -> item.getSupplierId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, NewStockPointVO> stockPointMap = stockPointList.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(),
                        (value1, value2) -> value1));
        String orgId = "323";
        List<NewStockPointVO> stockPointSJGList = newMdsFeign.selectStockPointByParams(scenario.getData(),
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "organizeId", orgId));
        NewStockPointVO newStockPointVO = stockPointSJGList.get(0);
        Map<String, NewProductStockPointVO> productCodeMap = getProductMapByCodes(productCodes, scenario.getData(),
                newStockPointVO.getStockPointCode());
        Map<String, SupplierAddressVO> supplierAddressMap = getSupplierAddressMapByCodes(supplierIds, scenario.getData());
        Map<String, User> userCodeMap = getUserMapByCodes();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        chooseIssueList.forEach(e -> {
            ErpPoCreate erpPoCreate = new ErpPoCreate();
            if (!stockPointMap.containsKey(e.getStockPointCode())) {
                throw new BusinessException(e.getProductCode() + "未找到对应公司");
            }
            NewStockPointVO newStockPointVO1 = stockPointMap.get(e.getStockPointCode());
            if (!productCodeMap.containsKey(e.getProductCode())) {
                throw new BusinessException(e.getProductCode() + "该编码未在物料表中存在");
            }
            NewProductStockPointVO newProductStockPointVO = productCodeMap.get(e.getProductCode());
            if (StringUtils.isEmpty(newProductStockPointVO.getPurchasePlanner())) {
                throw new BusinessException(e.getProductCode() + "采购计划员为空");
            }
            if (!userCodeMap.containsKey(newProductStockPointVO.getPurchasePlanner())) {
                throw new BusinessException(e.getProductCode() + "未找到对应采购计划员:" + newProductStockPointVO.getPurchasePlanner());
            }
            User user = userCodeMap.get(newProductStockPointVO.getPurchasePlanner());
            if (StringUtils.isEmpty(user.getErpUser())) {
                throw new BusinessException("请先维护" + e.getProductCode() + "对应的计划员ERP账号");
            }
            if (StringUtils.isEmpty(newStockPointVO1.getSupplierId())) {
                throw new BusinessException("请先维护" + e.getStockPointCode() + "对应的供应商关系");
            }
            if (StringUtils.isEmpty(newStockPointVO1.getCompanyName())) {
                throw new BusinessException("请先维护" + e.getStockPointCode() + "对应的公司名称");
            }
            if (StringUtils.isEmpty(supplierAddressMap.get(newStockPointVO1.getSupplierId()))) {
                throw new BusinessException("该" + e.getStockPointCode() + "对应的供应商没有采购的地址");
            }
            if (e.getOrderPrice() == null) {
                throw new BusinessException("价格为空");
            }
            erpPoCreate.setOrgId(orgId);
            erpPoCreate.setVendorName(newStockPointVO1.getCompanyName());
            erpPoCreate.setVendorSite(supplierAddressMap.get(newStockPointVO1.getSupplierId()).getAddress());
            erpPoCreate.setPoType("标准采购订单");
            erpPoCreate.setShipTo(newStockPointVO.getStockPointName());
            erpPoCreate.setBillTo(newStockPointVO.getStockPointName());
            erpPoCreate.setBuyer(user.getErpUser());
            erpPoCreate.setCurrency("CNY");
            erpPoCreate.setIsAutoApprave("Y");
            erpPoCreate.setBpimId(e.getId());
            erpPoCreate.setBpimStockPointCode(e.getStockPointCode());
            erpPoCreate.setBpimContainerNumber(e.getRemark());
            List<ErpPoCreate.PoLines> lines = new ArrayList<>();
            ErpPoCreate.PoLines poLines = new ErpPoCreate.PoLines();
            poLines.setItemCode(e.getProductCode());
            poLines.setUom("平方米");
            BigDecimal orderPrice = e.getOrderPrice();
            BigDecimal divisor = new BigDecimal("1.13");
            BigDecimal calculatedPrice = orderPrice.divide(divisor, 4, BigDecimal.ROUND_HALF_UP);
            poLines.setPrice(calculatedPrice);
            poLines.setQty(e.getArea());
            Date deliveryTime = e.getDeliveryTime();
            LocalDate localDate = deliveryTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate newDeliveryTime = localDate.plusDays(3);
            String formattedDate = newDeliveryTime.format(formatter);
            poLines.setNeedByDate(formattedDate);
            poLines.setNote("BPIM");
            lines.add(poLines);
            erpPoCreate.setLines(lines);
            erpPoCreateList.add(erpPoCreate);
        });
    }

    private Map<String, NewProductStockPointVO> getProductMapByCodes(List<String> productCodes, String scenario, String plantCode) {

        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointVOByParams(scenario,
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "productCodes", productCodes));
        Map<String, NewProductStockPointVO> productMap = productList.stream()
                .filter(item -> item.getStockPointCode().equals(plantCode))
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                        (value1, value2) -> value1));
        return productMap;
    }

    private Map<String, SupplierAddressVO> getSupplierAddressMapByCodes(List<String> supplierAddressList, String scenario) {

        List<SupplierAddressVO> productList = newMdsFeign.selectSupplierAddressByParams(scenario,
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "purchasingFlag", YesOrNoEnum.YES.getCode(),
                        "supplierIds", supplierAddressList));
        Map<String, SupplierAddressVO> supplierAddressMap = productList.stream()
                .collect(Collectors.toMap(SupplierAddressVO::getSupplierId, Function.identity(),
                        (value1, value2) -> value1));
        return supplierAddressMap;
    }

    private Map<String, User> getUserMapByCodes() {
        List<User> userList = ipsNewFeign.userList();
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(User::getId, Function.identity(),
                        (value1, value2) -> value1));
        return userMap;
    }
}
