package com.yhl.scp.ips.api.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.ips.api.dto.ExtApiLogDTO;
import com.yhl.scp.ips.api.vo.ExtApiLogVO;

import java.util.List;
import java.util.Map;

/**
 * <code>ExtApiLogService</code>
 * <p>
 * 接口日志表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-22 14:41:59
 */
public interface ExtApiLogService extends BaseService<ExtApiLogDTO, ExtApiLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link ExtApiLogVO}
     */
    List<ExtApiLogVO> selectAll();

    PageInfo<ExtApiLogVO> queryData(Pagination pagination, Map<String, Object> apiLogVO);

    ExtApiLogVO interfaceWarning( String configId);
}
