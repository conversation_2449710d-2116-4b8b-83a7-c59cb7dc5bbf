package com.yhl.scp.ips.api.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ExtApiLogVO</code>
 * <p>
 * 接口日志表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-22 14:41:59
 */
@ApiModel(value = "接口日志表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtApiLogVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 235309144535317640L;

    /**
     * 父日志id
     */
    @ApiModelProperty(value = "父日志id")
    @FieldInterpretation(value = "父日志id")
    private String parentId;
    /**
     * 配置ID
     */
    @ApiModelProperty(value = "配置ID")
    @FieldInterpretation(value = "配置ID")
    private String configId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    @FieldInterpretation(value = "批次号")
    private String batchNo;
    /**
     * 子批次号
     */
    @ApiModelProperty(value = "子批次号")
    @FieldInterpretation(value = "子批次号")
    private String subBatchNo;
    /**
     * 请求方式
     */
    @ApiModelProperty(value = "请求方式")
    @FieldInterpretation(value = "请求方式")
    private String requestType;
    /**
     * 触发方式（手动、自动）
     */
    @ApiModelProperty(value = "触发方式（手动、自动）")
    @FieldInterpretation(value = "触发方式（手动、自动）")
    private String triggerType;
    /**
     * 请求头
     */
    @ApiModelProperty(value = "请求头")
    @FieldInterpretation(value = "请求头")
    private String requestHeaders;
    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    @FieldInterpretation(value = "请求参数")
    private String requestParams;
    /**
     * 请求体
     */
    @ApiModelProperty(value = "请求体")
    @FieldInterpretation(value = "请求体")
    private String requestBody;
    /**
     * 响应头
     */
    @ApiModelProperty(value = "响应头")
    @FieldInterpretation(value = "响应头")
    private String responseHeaders;
    /**
     * 响应体
     */
    @ApiModelProperty(value = "响应体")
    @FieldInterpretation(value = "响应体")
    private String responseBody;
    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    @FieldInterpretation(value = "响应状态")
    private String responseStatus;
    /**
     * 请求时间
     */
    @ApiModelProperty(value = "请求时间")
    @FieldInterpretation(value = "请求时间")
    private Date requestTime;
    /**
     * 响应时间
     */
    @ApiModelProperty(value = "响应时间")
    @FieldInterpretation(value = "响应时间")
    private Date responseTime;
    /**
     * 解析状态
     */
    @ApiModelProperty(value = "解析状态")
    @FieldInterpretation(value = "解析状态")
    private String resolveStatus;
    /**
     * 解析数量
     */
    @ApiModelProperty(value = "解析数量")
    @FieldInterpretation(value = "解析数量")
    private Integer resolveCount;
    /**
     * 应用数量
     */
    @ApiModelProperty(value = "应用数量")
    @FieldInterpretation(value = "应用数量")
    private Integer applyCount;
    /**
     * 运行状态
     */
    @ApiModelProperty(value = "运行状态")
    @FieldInterpretation(value = "运行状态")
    private String status;
    /**
     * 请求流水号
     */
    @ApiModelProperty(value = "请求流水号")
    @FieldInterpretation(value = "请求流水号")
    private String serialNum;
    @ApiModelProperty(value = "${column.comment}")
    @FieldInterpretation(value = "${column.comment}")
    private Date lastUpdateTime;
    /**
     * 接口分类（鉴权，物料，客户等）
     */
    @ApiModelProperty(value = "接口分类（鉴权，物料，客户等）")
    private String apiCategory;
    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称")
    private String apiName;
    /**
     * 接口来源
     */
    @ApiModelProperty(value = "接口来源")
    private String apiSource;
    @Override
    public void clean() {

    }

}
