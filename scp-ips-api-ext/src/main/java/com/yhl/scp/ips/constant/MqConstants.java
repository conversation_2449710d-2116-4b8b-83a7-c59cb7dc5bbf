package com.yhl.scp.ips.constant;


/**
 * <AUTHOR>
 */
public class MqConstants {

    private static final String ACTIVE_PROFILE = System.getProperty("spring.profiles.active", "dev");

    public static final String BPIM_EVENT_EXCHANGE = ACTIVE_PROFILE + ".bpim_event_exchange";

    public static final String DATA_RECORD_CHANGE_ROUTING_KEY = ACTIVE_PROFILE + ".routing.data.record.change";

    public static final String DATA_RECORD_CHANGE_QUEUE = ACTIVE_PROFILE + ".queue.data.record.change";

    public static final String URL_TABLE_ROUTING_KEY = ACTIVE_PROFILE + ".routing.url.table";

    public static final String URL_TABLE_CHANGE_QUEUE = ACTIVE_PROFILE + ".queue.url.table";

    public static final String REQUEST_LOG_ROUTING_KEY = ACTIVE_PROFILE + ".routing.request.log";

    public static final String REQUEST_LOG_QUEUE = ACTIVE_PROFILE + ".queue.request.log";
}
