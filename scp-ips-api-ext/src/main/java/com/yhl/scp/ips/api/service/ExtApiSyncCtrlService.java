package com.yhl.scp.ips.api.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.ips.api.dto.ExtApiSyncCtrlDTO;
import com.yhl.scp.ips.api.vo.ExtApiSyncCtrlVO;

import java.util.List;
import java.util.Map;

/**
 * <code>ExtApiSyncCtrlService</code>
 * <p>
 * 外部api同步控制表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-22 14:46:58
 */
public interface ExtApiSyncCtrlService extends BaseService<ExtApiSyncCtrlDTO, ExtApiSyncCtrlVO> {

    /**
     * 查询所有
     *
     * @return list {@link ExtApiSyncCtrlVO}
     */
    List<ExtApiSyncCtrlVO> selectAll();

    PageInfo<ExtApiSyncCtrlVO> queryData(Pagination pagination, Map<String, Object> map);
}
