package com.yhl.scp.ips.config;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.log.dto.DataChangeRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.SqlSession;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 */
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
@Component
@Slf4j
public class MybatisChangeRecordInterceptor implements Interceptor, ApplicationContextAware {

    public static final String OPERATE_INSERT = "INSERT";
    public static final String OPERATE_UPDATE = "UPDATE";
    public static final String OPERATE_DELETE = "DELETE";
    /**
     * insert相关方法
     */
    public static final List<String> INTERCEPT_INSERT_METHODS = Lists.newArrayList("insert", "insertWithPrimaryKey", "insertBatch", "insertBatchWithPrimaryKey");
    public static final List<String> INTERCEPT_UPDATE_METHODS = Lists.newArrayList("update", "updateByPrimaryKey", "updateSelective", "updateBatch", "updateBatchSelective");
    public static final List<String> INTERCEPT_DELETE_METHODS = Lists.newArrayList("deleteByPrimaryKey", "deleteBatch");
    private static ApplicationContext applicationContext;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            // 获取完整 SQL ID
            String msId = mappedStatement.getId();
            // 提取方法名
            String methodName = msId.substring(msId.lastIndexOf(".") + 1);
            // 获取数据表名称
            Object parameter = invocation.getArgs()[1];
            String sql = mappedStatement.getBoundSql(parameter).getSql();
            String tableName = extractTableNameFromSql(sql);
            if (INTERCEPT_INSERT_METHODS.contains(methodName)) {
                handleWithInsert(invocation, msId, tableName);
            } else if (INTERCEPT_UPDATE_METHODS.contains(methodName)) {
                handleWithUpdate(invocation, msId, tableName);
            } else if (INTERCEPT_DELETE_METHODS.contains(methodName)) {
                handleWithDelete(invocation, msId, tableName);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("数据变更记录处理异常：{}", ex.getMessage());
        }
        return invocation.proceed();
    }

    /**
     * 处理插入方法的数据
     *
     * @param invocation
     */
    private void handleWithInsert(Invocation invocation, String msId, String tableName) {
        Object parameter = invocation.getArgs()[1];
        if (Objects.isNull(parameter)) {
            return;
        }
        List<Object> parameterList = Lists.newArrayList();
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(parameter));
        log.info("mybatis的insert方法拦截参数json:{}", json);
        if (json.containsKey("list")) {
            parameterList.addAll(Arrays.asList(json.getJSONArray("list").toArray()));
        } else {
            parameterList.add(parameter);
        }
        Class<?> originClazz = getClazzByMsId(msId);
        Map<String, String> nameDescriptionMap = MapUtil.newHashMap();
        Type[] genericInterfaces = originClazz.getGenericInterfaces();
        if (genericInterfaces.length > 0) {
            Class<?> returnType = (Class) ((ParameterizedType) genericInterfaces[0]).getActualTypeArguments()[1];
            nameDescriptionMap.putAll(getNameDescriptionMap(returnType));
        }
        List<DataChangeRecordDTO> dataChangeRecords = Lists.newArrayList();

        for (Object obj : parameterList) {
            if (Objects.isNull(obj)) {
                continue;
            }

            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
            JSONObject parameterJson = new JSONObject();

            jsonObject.keySet().forEach(key -> parameterJson.put(nameDescriptionMap.getOrDefault(key, key), jsonObject.get(key)));

            DataChangeRecordDTO recordDTO = DataChangeRecordDTO.builder().id(UUIDUtil.getUUID()).dataTableName(tableName).primaryId(parameterJson.getString("id")).beforeData("").afterData(parameterJson.toJSONString()).operateUser(SystemHolder.getUserId()).operateType(OPERATE_INSERT).versionValue(1).createTime(new Date()).modifyTime(new Date()).build();
            dataChangeRecords.add(recordDTO);
        }

        if (CollectionUtils.isNotEmpty(dataChangeRecords)) {
            dataChangeRecords.forEach(this::sendMessage);
        }
    }

    /**
     * 处理更新方法的数据
     *
     * @param invocation
     */
    private void handleWithUpdate(Invocation invocation, String msId, String tableName) {
        Object parameter = invocation.getArgs()[1];
        if (Objects.isNull(parameter)) {
            return;
        }
        List<Object> parameterList = Lists.newArrayList();
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(parameter));
        log.info("mybatis的update方法拦截参数json:{}", json);
        if (json.containsKey("list")) {
            parameterList.addAll(Arrays.asList(json.getJSONArray("list").toArray()));
        } else {
            parameterList.add(parameter);
        }
        String userId = SystemHolder.getUserId();
        List<DataChangeRecordDTO> dataChangeRecords = Lists.newArrayList();

        Object bean = getBeanByClazz(msId);
        Class<?> originClazz = getClazzByMsId(msId);
        Method method = getMethodByPrimaryKey(bean);
        if (Objects.isNull(method)) {
            log.error("未找到对应的方法：{}", msId);
            return;
        }
        Map<String, String> nameDescriptionMap = MapUtil.newHashMap();
        Type[] genericInterfaces = originClazz.getGenericInterfaces();
        if (genericInterfaces.length > 0) {
            Class<?> returnType = (Class) ((ParameterizedType) genericInterfaces[0]).getActualTypeArguments()[1];
            nameDescriptionMap.putAll(getNameDescriptionMap(returnType));
        }
        for (Object object : parameterList) {
            if (Objects.isNull(object)) {
                continue;
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
            String primaryId = jsonObject.getString("id");
            JSONObject parameterJson = new JSONObject();
            jsonObject.keySet().forEach(key -> parameterJson.put(nameDescriptionMap.getOrDefault(key, key), jsonObject.get(key)));
            DataChangeRecordDTO recordDTO = DataChangeRecordDTO.builder().id(UUIDUtil.getUUID()).dataTableName(tableName).primaryId(primaryId).beforeData(getResultByPrimaryId(bean, method, primaryId, nameDescriptionMap)).afterData(parameterJson.toJSONString()).operateUser(userId).operateType(OPERATE_UPDATE).versionValue(1).createTime(new Date()).modifyTime(new Date()).build();
            dataChangeRecords.add(recordDTO);
        }
        if (CollectionUtils.isNotEmpty(dataChangeRecords)) {
            dataChangeRecords.forEach(this::sendMessage);
        }
    }

    /**
     * 处理删除方法的数据
     *
     * @param invocation
     */
    private void handleWithDelete(Invocation invocation, String msId, String tableName) {
        Object parameter = invocation.getArgs()[1];
        if (Objects.isNull(parameter)) {
            return;
        }
        List<String> parameterList = Lists.newArrayList();
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(parameter));
        log.info("mybatis的delete方法拦截参数json:{}", json);
        if (json.containsKey("ids")) {
            for (Object object : json.getJSONArray("ids")) {
                parameterList.add(object.toString());
            }
        } else {
            parameterList.add(parameter.toString());
        }

        String userId = SystemHolder.getUserId();
        List<DataChangeRecordDTO> dataChangeRecords = Lists.newArrayList();

        Object bean = getBeanByClazz(msId);
        Class<?> originClazz = getClazzByMsId(msId);
        Method method = getMethodByPrimaryKey(bean);
        if (Objects.isNull(method)) {
            log.error("未找到对应的方法：{}", msId);
            return;
        }
        Map<String, String> nameDescriptionMap = MapUtil.newHashMap();
        Type[] genericInterfaces = originClazz.getGenericInterfaces();
        if (genericInterfaces.length > 0) {
            Class<?> returnType = (Class) ((ParameterizedType) genericInterfaces[0]).getActualTypeArguments()[1];
            nameDescriptionMap.putAll(getNameDescriptionMap(returnType));
        }
        for (String primaryId : parameterList) {
            if (StringUtils.isBlank(primaryId)) {
                continue;
            }
            DataChangeRecordDTO recordDTO = DataChangeRecordDTO.builder().id(UUIDUtil.getUUID()).dataTableName(tableName).primaryId(primaryId).beforeData(getResultByPrimaryId(bean, method, primaryId, nameDescriptionMap)).afterData("").operateUser(userId).operateType(OPERATE_DELETE).versionValue(1).createTime(new Date()).modifyTime(new Date()).build();
            dataChangeRecords.add(recordDTO);
        }
        if (CollectionUtils.isNotEmpty(dataChangeRecords)) {
            dataChangeRecords.forEach(this::sendMessage);
        }
    }

    /**
     * 从SQL语句中提取表名
     *
     * @param sql SQL语句
     * @return 表名
     */
    private String extractTableNameFromSql(String sql) {
        if (sql.contains("(")) {
            sql = sql.substring(0, sql.indexOf("("));
        }
        String trimSql = sql.trim();
        // 去除所有引号和换行符
        String processedSql = trimSql.replaceAll("[`'\"]", "").replaceAll("\\s+", " ");

        String tableName = "";
        if (processedSql.toUpperCase().startsWith("INSERT INTO")) {
            String[] parts = processedSql.split(" ");
            tableName = parts.length > 2 ? parts[2].split("\\.")[parts[2].split("\\.").length - 1] : processedSql;
        } else if (processedSql.toUpperCase().startsWith("UPDATE")) {
            String[] parts = processedSql.split(" ");
            tableName = parts.length > 1 ? parts[1].split("\\.")[parts[1].split("\\.").length - 1] : processedSql;
        } else if (processedSql.toUpperCase().startsWith("DELETE FROM")) {
            String[] parts = processedSql.split(" ");
            tableName = parts.length > 2 ? parts[2].split("\\.")[parts[2].split("\\.").length - 1] : processedSql;
        }
        if (StringUtils.isNotBlank(tableName)) {
            return tableName.replace("(", "");
        }
        return processedSql;
    }

    /**
     * 获取类字段的名称Map
     *
     * @param parameter
     * @return
     */
    private Map<String, String> getNameDescriptionMap(Object parameter) {
        Map<String, String> nameDescriptionMap = MapUtil.newHashMap();
        if (Objects.isNull(parameter)) {
            return nameDescriptionMap;
        }
        try {
            Class<?> clazz = getClazzByParameter(parameter);
            if (Objects.isNull(clazz)) {
                return nameDescriptionMap;
            }
            nameDescriptionMap.putAll(getNameDescriptionMap(clazz));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("组合类字段的名称Map失败：{}", ex.getMessage());
        }
        return nameDescriptionMap;
    }

    /**
     * 获取类字段的名称Map
     *
     * @param clazz
     * @return
     */
    private Map<String, String> getNameDescriptionMap(Class<?> clazz) {
        Map<String, String> nameDescriptionMap = MapUtil.newHashMap();
        try {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                String value = Objects.nonNull(annotation) ? annotation.value() : name;
                nameDescriptionMap.put(name, value);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("组合类字段的名称Map失败：{}", ex.getMessage());
        }
        return nameDescriptionMap;
    }


    /**
     * 获取类字段的Class
     *
     * @param parameter
     * @return
     */
    private Class<?> getClazzByParameter(Object parameter) {
        Class<?> clazz = null;
        if (Objects.isNull(parameter)) {
            return clazz;
        }
        if (parameter instanceof List) {
            List<?> list = (List<?>) parameter;
            Optional<?> optional = list.stream().filter(Objects::nonNull).findFirst();
            if (!optional.isPresent()) {
                return clazz;
            }
            Object o = optional.get();
            clazz = o.getClass();
        } else {
            clazz = parameter.getClass();
        }
        return clazz;
    }

    private Class<?> getClazzByMsId(String msId) {
        String className = msId.substring(0, msId.lastIndexOf("."));
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据主键ID获取结果
     *
     * @param bean
     * @param method
     * @param id
     * @param nameDescriptionMap
     * @return
     */
    private String getResultByPrimaryId(Object bean, Method method, String id, Map<String, String> nameDescriptionMap) {
        try {
            if (Objects.isNull(bean)) {
                return null;
            }
            method.setAccessible(true);
            SqlSession sqlSession = SpringBeanUtils.getBean(SqlSession.class);
            if (Objects.nonNull(sqlSession)) {
                sqlSession.clearCache();
            }
            Object object = method.invoke(bean, id);
            if (Objects.isNull(object)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
            JSONObject parameterJson = new JSONObject();
            jsonObject.keySet().forEach(key -> parameterJson.put(nameDescriptionMap.getOrDefault(key, key), jsonObject.get(key)));
            return parameterJson.toJSONString();
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("获取bean失败：" + ex.getMessage());
        }
        return null;
    }

    /**
     * 根据 msId 获取对应的 bean
     *
     * @param msId
     * @return
     */
    private Object getBeanByClazz(String msId) {
        String className = msId.substring(0, msId.lastIndexOf("."));
        try {
            Class<?> clazz = Class.forName(className);
            Map<String, ?> beansOfTypeMap = applicationContext.getBeansOfType(clazz);
            if (MapUtil.isEmpty(beansOfTypeMap)) {
                throw new RuntimeException("No bean found for class: " + clazz.getName());
            }
            // 默认选择第一个bean
            return beansOfTypeMap.values().iterator().next();
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private Method getMethodByPrimaryKey(Object bean) {
        try {
            Class<?> clazz = bean.getClass();
            return clazz.getDeclaredMethod("selectByPrimaryKey", String.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("获取method失败：" + ex.getMessage());
        }
        return null;
    }

    /**
     * 发送消息
     *
     * @param recordDTO
     */
    private void sendMessage(DataChangeRecordDTO recordDTO) {
        try {
            if (Objects.isNull(rabbitTemplate)) {
                log.error("RabbitTemplate is null, get from spring");
                rabbitTemplate = SpringBeanUtils.getBean(RabbitTemplate.class);
            }
            rabbitTemplate.convertAndSend(MqConstants.BPIM_EVENT_EXCHANGE, MqConstants.DATA_RECORD_CHANGE_ROUTING_KEY, JSONObject.toJSONString(recordDTO));
        } catch (Exception e) {
            log.error("Failed to send message for record: {}, error: {}", recordDTO, e.getMessage());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
