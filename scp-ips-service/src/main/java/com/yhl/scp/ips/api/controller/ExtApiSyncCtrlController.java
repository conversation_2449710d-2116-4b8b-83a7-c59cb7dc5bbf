package com.yhl.scp.ips.api.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.api.dto.ExtApiSyncCtrlDTO;
import com.yhl.scp.ips.api.service.ExtApiSyncCtrlService;
import com.yhl.scp.ips.api.vo.ExtApiLogVO;
import com.yhl.scp.ips.api.vo.ExtApiSyncCtrlVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Slf4j
@Api(tags = "外部api同步控制表控制器")
@RestController
@RequestMapping("extApiSyncCtrl")
public class ExtApiSyncCtrlController extends BaseController {

    @Resource
    private ExtApiSyncCtrlService extApiSyncCtrlService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<ExtApiSyncCtrlVO>> page() {
        List<ExtApiSyncCtrlVO> extApiSyncCtrlList = extApiSyncCtrlService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ExtApiSyncCtrlVO> pageInfo = new PageInfo<>(extApiSyncCtrlList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ExtApiSyncCtrlDTO extApiSyncCtrlDTO) {
        return extApiSyncCtrlService.doCreate(extApiSyncCtrlDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ExtApiSyncCtrlDTO extApiSyncCtrlDTO) {
        return extApiSyncCtrlService.doUpdate(extApiSyncCtrlDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        extApiSyncCtrlService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<ExtApiSyncCtrlVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, extApiSyncCtrlService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "查询数据")
    @PostMapping(value = "queryData")
    public BaseResponse<PageInfo<ExtApiSyncCtrlVO>> queryData(@RequestBody Map<String,Object> map) {
        PageInfo<ExtApiSyncCtrlVO> pageInfo = extApiSyncCtrlService.queryData(getPagination(),map);
        return BaseResponse.success(pageInfo);
    }

}
