package com.yhl.scp.ips.api.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO;
import com.yhl.scp.ips.api.vo.ExtApiSyncCtrlVO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;


public interface ExtApiSyncCtrlDao extends BaseDao<ExtApiSyncCtrlPO, ExtApiSyncCtrlVO> {

    long selectCountByParams(@Param("params") Map<String, Object> params);
}
