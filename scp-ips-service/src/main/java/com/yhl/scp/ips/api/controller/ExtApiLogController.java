package com.yhl.scp.ips.api.controller;

import com.github.pagehelper.PageInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.api.dto.ExtApiLogDTO;
import com.yhl.scp.ips.api.service.ExtApiLogService;
import com.yhl.scp.ips.api.vo.ExtApiLogVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Slf4j
@Api(tags = "接口日志表控制器")
@RestController
@RequestMapping("extApiLog")
public class ExtApiLogController extends BaseController {

    @Resource
    private ExtApiLogService extApiLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<ExtApiLogVO>> page() {
        List<ExtApiLogVO> extApiLogList = extApiLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ExtApiLogVO> pageInfo = new PageInfo<>(extApiLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ExtApiLogDTO extApiLogDTO) {
        return extApiLogService.doCreate(extApiLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ExtApiLogDTO extApiLogDTO) {
        return extApiLogService.doUpdate(extApiLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        extApiLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<ExtApiLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, extApiLogService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "查询数据")
    @PostMapping(value = "queryData")
    public BaseResponse<PageInfo<ExtApiLogVO>> queryData(@RequestBody Map<String,Object> map) {
        PageInfo<ExtApiLogVO> pageInfo = extApiLogService.queryData(getPagination(),map);
        return BaseResponse.success(pageInfo);
    }

    @ApiOperation(value = "同步接口预警")
    @GetMapping(value = "interfaceWarning/{configId}")
    public BaseResponse<ExtApiLogVO> interfaceWarning(@PathVariable(name = "configId") String configId) {
        ExtApiLogVO extApiLogVOS = extApiLogService.interfaceWarning(configId);
        return BaseResponse.success(extApiLogVOS);
    }
}
