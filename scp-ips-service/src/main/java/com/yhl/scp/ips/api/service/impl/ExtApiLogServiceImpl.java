package com.yhl.scp.ips.api.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.api.convertor.ExtApiLogConvertor;
import com.yhl.scp.ips.api.domain.entity.ExtApiLogDO;
import com.yhl.scp.ips.api.domain.service.ExtApiLogDomainService;
import com.yhl.scp.ips.api.dto.ExtApiLogDTO;
import com.yhl.scp.ips.api.infrastructure.dao.ExtApiConfigDao;
import com.yhl.scp.ips.api.infrastructure.dao.ExtApiLogDao;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiConfigPO;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiLogPO;
import com.yhl.scp.ips.api.service.ExtApiLogService;
import com.yhl.scp.ips.api.vo.ExtApiLogVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ExtApiLogServiceImpl extends AbstractService implements ExtApiLogService {

    @Resource
    private ExtApiLogDao extApiLogDao;

    @Resource
    private ExtApiLogDomainService extApiLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private ExtApiConfigDao extApiConfigDao;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ExtApiLogDTO extApiLogDTO) {
        // 0.数据转换
        ExtApiLogDO extApiLogDO = ExtApiLogConvertor.INSTANCE.dto2Do(extApiLogDTO);
        ExtApiLogPO extApiLogPO = ExtApiLogConvertor.INSTANCE.dto2Po(extApiLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiLogDomainService.validation(extApiLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(extApiLogPO);
        extApiLogDao.insert(extApiLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ExtApiLogDTO extApiLogDTO) {
        // 0.数据转换
        ExtApiLogDO extApiLogDO = ExtApiLogConvertor.INSTANCE.dto2Do(extApiLogDTO);
        ExtApiLogPO extApiLogPO = ExtApiLogConvertor.INSTANCE.dto2Po(extApiLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiLogDomainService.validation(extApiLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(extApiLogPO);
        extApiLogDao.update(extApiLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ExtApiLogDTO> list) {
        List<ExtApiLogPO> newList = ExtApiLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        extApiLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ExtApiLogDTO> list) {
        List<ExtApiLogPO> newList = ExtApiLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        extApiLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return extApiLogDao.deleteBatch(idList);
        }
        return extApiLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ExtApiLogVO selectByPrimaryKey(String id) {
        ExtApiLogPO po = extApiLogDao.selectByPrimaryKey(id);
        return ExtApiLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "EXT_API_LOG")
    public List<ExtApiLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "EXT_API_LOG")
    public List<ExtApiLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ExtApiLogVO> dataList = extApiLogDao.selectByCondition(sortParam, queryCriteriaParam);
        ExtApiLogServiceImpl target = springBeanUtils.getBean(ExtApiLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ExtApiLogVO> selectByParams(Map<String, Object> params) {
        List<ExtApiLogPO> list = extApiLogDao.selectByParams(params);
        return ExtApiLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ExtApiLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public PageInfo<ExtApiLogVO> queryData(Pagination pagination, Map<String, Object> params) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<ExtApiLogPO> list = extApiLogDao.selectByParams(params);
        List<ExtApiLogVO> extApiLogVOS = ExtApiLogConvertor.INSTANCE.po2Vos(list);
        PageInfo<ExtApiLogVO> pageInfo = new PageInfo<>(extApiLogVOS);
        //获取查询数据的接口id集合
        List<String> ids = extApiLogVOS.stream()
                .map(ExtApiLogVO::getConfigId).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<ExtApiConfigPO> extApiConfigPOS = extApiConfigDao.selectByPrimaryKeys(ids);
            //按照id分类
            Map<String, List<ExtApiConfigPO>> collect =
                    extApiConfigPOS.stream().collect(Collectors.groupingBy(ExtApiConfigPO::getId));
            extApiLogVOS.stream().forEach(item -> {
                if (collect.containsKey(item.getConfigId())) {
                    ExtApiConfigPO extApiConfigPO = collect.get(item.getConfigId()).get(0);
                    item.setApiCategory(extApiConfigPO.getApiCategory());
                    item.setApiName(extApiConfigPO.getApiName());
                    item.setApiSource(extApiConfigPO.getApiSource());
                }
            });
            long count = extApiLogDao.selectCountByParams(params);
            pageInfo.setTotal(count);
        }
        return pageInfo;
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EXT_API_LOG.getCode();
    }

    @Override
    public List<ExtApiLogVO> invocation(List<ExtApiLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public ExtApiLogVO interfaceWarning(String configId) {
        ExtApiLogVO extApiLogVO = extApiLogDao.selectLatestByConfigId(configId);
        return extApiLogVO;
    }

}
