<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.api.infrastructure.dao.ExtApiSyncCtrlDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO">
        <!--@Table ext_api_sync_ctrl-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="api_config_id" jdbcType="VARCHAR" property="apiConfigId"/>
        <result column="refer_value" jdbcType="VARCHAR" property="referValue"/>
        <result column="group_value" jdbcType="VARCHAR" property="groupValue"/>
        <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.api.vo.ExtApiSyncCtrlVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,api_config_id,refer_value,group_value,sync_time,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.apiConfigId != null and params.apiConfigId != ''">
                and api_config_id = #{params.apiConfigId,jdbcType=VARCHAR}
            </if>
            <if test="params.referValue != null and params.referValue != ''">
                and refer_value = #{params.referValue,jdbcType=VARCHAR}
            </if>
            <if test="params.groupValue != null and params.groupValue != ''">
                and group_value = #{params.groupValue,jdbcType=VARCHAR}
            </if>
            <if test="params.syncTime != null">
                and sync_time = #{params.syncTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ext_api_sync_ctrl
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ext_api_sync_ctrl
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from ext_api_sync_ctrl
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ext_api_sync_ctrl
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectCountByParams" resultType="java.lang.Long">
        select count(*)
        from ext_api_sync_ctrl
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into ext_api_sync_ctrl(
        id,
        api_config_id,
        refer_value,
        group_value,
        sync_time,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{apiConfigId,jdbcType=VARCHAR},
        #{referValue,jdbcType=VARCHAR},
        #{groupValue,jdbcType=VARCHAR},
        #{syncTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO">
        insert into ext_api_sync_ctrl(
        id,
        api_config_id,
        refer_value,
        group_value,
        sync_time,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{apiConfigId,jdbcType=VARCHAR},
        #{referValue,jdbcType=VARCHAR},
        #{groupValue,jdbcType=VARCHAR},
        #{syncTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into ext_api_sync_ctrl(
        id,
        api_config_id,
        refer_value,
        group_value,
        sync_time,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.apiConfigId,jdbcType=VARCHAR},
        #{entity.referValue,jdbcType=VARCHAR},
        #{entity.groupValue,jdbcType=VARCHAR},
        #{entity.syncTime,jdbcType=TIMESTAMP},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into ext_api_sync_ctrl(
        id,
        api_config_id,
        refer_value,
        group_value,
        sync_time,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.apiConfigId,jdbcType=VARCHAR},
        #{entity.referValue,jdbcType=VARCHAR},
        #{entity.groupValue,jdbcType=VARCHAR},
        #{entity.syncTime,jdbcType=TIMESTAMP},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO">
        update ext_api_sync_ctrl set
        api_config_id = #{apiConfigId,jdbcType=VARCHAR},
        refer_value = #{referValue,jdbcType=VARCHAR},
        group_value = #{groupValue,jdbcType=VARCHAR},
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO">
        update ext_api_sync_ctrl
        <set>
            <if test="item.apiConfigId != null and item.apiConfigId != ''">
                api_config_id = #{item.apiConfigId,jdbcType=VARCHAR},
            </if>
            <if test="item.referValue != null and item.referValue != ''">
                refer_value = #{item.referValue,jdbcType=VARCHAR},
            </if>
            <if test="item.groupValue != null and item.groupValue != ''">
                group_value = #{item.groupValue,jdbcType=VARCHAR},
            </if>
            <if test="item.syncTime != null">
                sync_time = #{item.syncTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update ext_api_sync_ctrl
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="api_config_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.apiConfigId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="refer_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.referValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="group_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.groupValue,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sync_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.syncTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update ext_api_sync_ctrl 
        <set>
            <if test="item.apiConfigId != null and item.apiConfigId != ''">
                api_config_id = #{item.apiConfigId,jdbcType=VARCHAR},
            </if>
            <if test="item.referValue != null and item.referValue != ''">
                refer_value = #{item.referValue,jdbcType=VARCHAR},
            </if>
            <if test="item.groupValue != null and item.groupValue != ''">
                group_value = #{item.groupValue,jdbcType=VARCHAR},
            </if>
            <if test="item.syncTime != null">
                sync_time = #{item.syncTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from ext_api_sync_ctrl where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from ext_api_sync_ctrl where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
