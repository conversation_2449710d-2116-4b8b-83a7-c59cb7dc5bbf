package com.yhl.scp.ips.api.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiLogPO;
import com.yhl.scp.ips.api.vo.ExtApiLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;


public interface ExtApiLogDao extends BaseDao<ExtApiLogPO, ExtApiLogVO> {
    ExtApiLogVO selectLatestByConfigId(@Param("configId") String configId);

    long selectCountByParams(@Param("params") Map<String, Object> params);
}
