package com.yhl.scp.mds.common.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.MapUtils;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmProductionTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.algo.pojo.AlgorithmPolymerization;
import com.yhl.scp.mds.algo.service.MdsAlgoInputService;
import com.yhl.scp.mds.algorithmnew.input.pojo.MdsAlgorithmInputNew;
import com.yhl.scp.mds.algorithmnew.input.pojo.PlanHorizonInputNewData;
import com.yhl.scp.mds.async.ImdbAsync;
import com.yhl.scp.mds.baseResource.dto.PhysicalResourceLogDTO;
import com.yhl.scp.mds.baseResource.dto.StandardResourceDTO;
import com.yhl.scp.mds.baseResource.service.FyStandardResourceService;
import com.yhl.scp.mds.baseResource.service.PhysicalResourceLogService;
import com.yhl.scp.mds.basic.enums.CalendarRuleTypeEnum;
import com.yhl.scp.mds.basic.time.enums.DecisionTypeEnum;
import com.yhl.scp.mds.bom.dto.ProductAboutBomDTO;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.service.ProductAboutBomService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.bom.vo.ProductRiskLevelVO;
import com.yhl.scp.mds.box.service.BoxInfoService;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.calendar.convertor.CalendarRuleConvertor;
import com.yhl.scp.mds.calendar.convertor.ResourceCalendarConvertor;
import com.yhl.scp.mds.calendar.domain.service.CalendarRuleDomainService;
import com.yhl.scp.mds.calendar.dto.CalendarForFeedbackDTO;
import com.yhl.scp.mds.calendar.infrastructure.dao.CalendarRuleDao;
import com.yhl.scp.mds.calendar.infrastructure.dao.ResourceCalendarDao;
import com.yhl.scp.mds.calendar.service.CalendarRuleService;
import com.yhl.scp.mds.calendar.service.ResourceCalendarService;
import com.yhl.scp.mds.calendar.service.ShiftService;
import com.yhl.scp.mds.curingTime.service.MdsCuringTimeService;
import com.yhl.scp.mds.curingTime.service.MdsFinishedProductDeliveryService;
import com.yhl.scp.mds.customer.service.CustomerService;
import com.yhl.scp.mds.customer.vo.CustomerVO;
import com.yhl.scp.mds.deleteGroup.service.MdsDeleteGroupsService;
import com.yhl.scp.mds.extension.calendar.domain.entity.CalendarRuleDO;
import com.yhl.scp.mds.extension.calendar.dto.CalendarRuleDTO;
import com.yhl.scp.mds.extension.calendar.dto.ResourceCalendarDTO;
import com.yhl.scp.mds.extension.calendar.dto.ShiftDTO;
import com.yhl.scp.mds.extension.calendar.infrastructure.po.CalendarRulePO;
import com.yhl.scp.mds.extension.calendar.infrastructure.po.ResourceCalendarPO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.calendar.vo.ShiftVO;
import com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO;
import com.yhl.scp.mds.extension.param.vo.PlanCancellationParamVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.dto.RoutingStepDTO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingStepPO;
import com.yhl.scp.mds.extension.routing.infrastructure.po.RoutingStepResourcePO;
import com.yhl.scp.mds.extension.routing.vo.*;
import com.yhl.scp.mds.extension.rule.dto.RuleEncodingsDTO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.extension.time.vo.TimePeriodGroupVO;
import com.yhl.scp.mds.extension.time.vo.TimePeriodVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.mold.service.MoldToolingGroupService;
import com.yhl.scp.mds.mold.service.MoldToolingService;
import com.yhl.scp.mds.mold.vo.MoldToolingGroupVO;
import com.yhl.scp.mds.mold.vo.MoldToolingVO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.enums.NewProductEnum;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.organization.service.ProductionOrganizationService;
import com.yhl.scp.mds.overdeadlineday.service.MdsOverDeadlineDaysService;
import com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO;
import com.yhl.scp.mds.param.service.PlanCancellationParamService;
import com.yhl.scp.mds.product.convertor.ProductCandidateResourceTimeConvertor;
import com.yhl.scp.mds.product.dto.ResourceCalendarQueryDTO;
import com.yhl.scp.mds.product.infrastructure.dao.ProductCandidateResourceTimeDao;
import com.yhl.scp.mds.product.infrastructure.dao.ProductStockPointBaseDao;
import com.yhl.scp.mds.product.infrastructure.po.ProductCandidateResourceTimePO;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.product.vo.ProductDemandSupplyCalculateVO;
import com.yhl.scp.mds.productBox.service.ProductBoxRelationService;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.production.dto.ProductionOrganizeDTO;
import com.yhl.scp.mds.production.service.NewProductionOrganizationService;
import com.yhl.scp.mds.production.service.ProductionOrganizeService;
import com.yhl.scp.mds.production.vo.NewProductionOrganizationVO;
import com.yhl.scp.mds.production.vo.ProductionOrganizeVO;
import com.yhl.scp.mds.productroutestepbase.infrastructure.dao.MdsProductStockPointBaseDao;
import com.yhl.scp.mds.productroutestepbase.service.MdsProductStockPointBaseService;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.resource.infrastructure.dao.PhysicalResourceDao;
import com.yhl.scp.mds.resource.service.PhysicalResourceService;
import com.yhl.scp.mds.resource.service.StandardResourceService;
import com.yhl.scp.mds.routing.convertor.RoutingStepConvertor;
import com.yhl.scp.mds.routing.convertor.RoutingStepResourceConvertor;
import com.yhl.scp.mds.routing.infrastructure.dao.*;
import com.yhl.scp.mds.routing.service.*;
import com.yhl.scp.mds.routing.vo.BomTreeNewVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.rule.convertor.RuleEncodingsConvertor;
import com.yhl.scp.mds.rule.service.RuleEncodingsService;
import com.yhl.scp.mds.stock.dto.NewStockPointDTO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.dto.ProductSubstitutionRelationshipDTO;
import com.yhl.scp.mds.substitution.service.ProductSubstitutionRelationshipService;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mds.supplier.convertor.SupplierConvertor;
import com.yhl.scp.mds.supplier.infrastructure.dao.SupplierDataDao;
import com.yhl.scp.mds.supplier.service.SupplierAddressService;
import com.yhl.scp.mds.supplier.service.SupplierDataService;
import com.yhl.scp.mds.supplier.service.SupplierOwnerInfoService;
import com.yhl.scp.mds.supplier.vo.SupplierAddressVO;
import com.yhl.scp.mds.supplier.vo.SupplierPurchaseRationMap;
import com.yhl.scp.mds.sync.service.MdsWorkOrderSyncRoutingService;
import com.yhl.scp.mds.time.service.PlanningHorizonService;
import com.yhl.scp.mds.time.service.TimePeriodGroupService;
import com.yhl.scp.mds.time.service.TimePeriodService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>NewMdsFeignController</code>
 * <p>
 * NewMdsFeignController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 14:11:19
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class NewMdsFeignController implements NewMdsFeign {
    @Resource
    StandardResourceService standardResourceService;
    @Resource
    ProductSubstitutionRelationshipService productSubstitutionRelationshipService;
    @Resource
    SupplierAddressService supplierAddressService;
    @Resource
    RoutingStepDao routeStepDao;
    @Resource
    private NewProductStockPointService newProductStockPointService;
    @Resource
    private ResourceCalendarService resourceCalendarService;
    @Resource
    private RoutingService routingService;
    @Resource
    private RoutingStepService routingStepService;
    @Resource
    private NewStockPointService newStockPointService;
    @Resource
    private PlanningHorizonService planningHorizonService;
    @Resource
    private ProductionOrganizeService productionOrganizeService;
    @Resource
    private MdsAlgoInputService mdsAlgoInputService;
    @Resource
    private ResourceCalendarDao resourceCalendarDao;
    @Resource
    private PhysicalResourceService physicalResourceService;
    @Resource
    private ProductBoxRelationService productBoxRelationService;
    @Resource
    private BoxInfoService boxInfoService;
    @Resource
    private MdsProductStockPointBaseService mdsProductStockPointBaseService;
    @Resource
    private MdsProductStockPointBaseDao mdsProductStockPointBaseDao;
    @Resource
    private RoutingStepResourceService routingStepResourceService;
    @Resource
    private RoutingStepResourceDao routingStepResourceDao;
    @Resource
    private ProductCandidateResourceTimeDao productCandidateResourceTimeDao;
    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;
    @Resource
    private ProductionOrganizationService productionOrganizationService;
    @Resource
    private RoutingStepInputService routingStepInputService;
    @Resource
    private BomTreeNewDao bomTreeNewDao;
    @Resource
    private StandardStepService standardStepService;
    @Resource
    private RoutingStepOutputService routingStepOutputService;
    @Resource
    private NewProductionOrganizationService newProductionOrganizationService;
    @Resource
    private ShiftService shiftService;
    @Resource
    private CalendarRuleService calendarRuleService;
    @Resource
    private RoutingStepInputNewService routingStepInputNewService;
    @Resource
    private SupplierDataService supplierService;
    @Resource
    private SupplierDataDao supplierDataDao;
    @Resource
    private SupplierOwnerInfoService supplierOwnerInfoService;
    @Resource
    private ProductRoutingService productRoutingService;
    @Resource
    private MdsWorkOrderSyncRoutingService mdsWorkOrderSyncRoutingService;
    @Resource
    private TimePeriodGroupService timePeriodGroupService;
    @Resource
    private TimePeriodService timePeriodService;
    @Resource
    private RuleEncodingsService ruleEncodingsService;
    @Resource
    private PlanCancellationParamService planCancellationParamService;
    @Resource
    private MdsOpYieldService mdsOpYieldService;
    @Resource
    private MdsDeleteGroupsService mdsDeleteGroupsService;
    @Resource
    private ProductAboutBomService productAboutBomService;
    @Resource
    private FyStandardResourceService fyStandardResourceService;
    @Resource
    private PhysicalResourceLogService physicalResourceLogService;
    @Resource
    private NewRoutingService newRoutingService;
    @Resource
    private NewProductCandidateResourceService newProductCandidateResourceService;
    @Resource
    private NewProductCandidateResourceDao newProductCandidateResourceDao;
    @Resource
    private com.yhl.scp.mds.baseResource.service.PhysicalResourceService fyPhysicalResourceService;
    @Resource
    private CustomerService customerService;
    @Resource
    private MdsOverDeadlineDaysService mdsOverDeadlineDaysService;
    @Resource
    private CalendarRuleDao calendarRuleDao;
    @Resource
    private CalendarRuleDomainService calendarRuleDomainService;
    @Resource
    private ImdbAsync imdbAsync;
    @Resource
    private PhysicalResourceDao physicalResourceDao;
    @Resource
    private MdsProductBomService mdsProductBomService;
    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;
    @Resource
    private MoldToolingService moldToolingService;
    @Resource
    private MoldToolingGroupService moldToolingGroupService;
    @Resource
    private RoutingStepInputDao routingStepInputDao;
    @Resource
    private ProductStockPointBaseDao productStockPointBaseDao;
    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;
    @Resource
    private MdsCuringTimeService mdsCuringTimeService;
    @Resource
    private MdsFinishedProductDeliveryService mdsFinishedProductDeliveryService;
    @Resource
    private NewRoutingStepResourceService newRoutingStepResourceService;

    @Override
    public List<NewProductStockPointVO> selectProductListByParamOnDynamicColumns(String scenario, FeignDynamicParam feignDynamicParam) {
        return newProductStockPointService.selectProductListByParamOnDynamicColumns(feignDynamicParam.getDynamicColumnParam(), feignDynamicParam.getQueryParam());
    }

    @Override
    public List<NewProductStockPointVO> selectProductStockPointByParams(String scenario, Map<String, Object> params) {
        return newProductStockPointService.selectByParams(params);
    }

    @Override
    public List<String> selectProductCodeByParams(Map<String, Object> params) {
        return newProductStockPointService.selectProductCodeByParams(params);
    }

    @Override
    public List<NewProductStockPointVO> selectProductStockPointVOByParams(String scenario, Map<String, Object> params) {
        return newProductStockPointService.selectVOByParams(params);
    }

    @Override
    public List<NewProductStockPointVO> selectProductStockPointVOByProductCodes(String scenario,
                                                                                List<String> productCodes) {
        return newProductStockPointService.selectByProductCode(productCodes);
    }

    @Override
    public List<NewProductStockPointVO> selectByVehicleModelCode(String scenario, List<String> vehicleModelCodeList) {
        if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return newProductStockPointService.selectByVehicleModelCode(vehicleModelCodeList);
    }

    @Override
    public List<NewProductStockPointVO> selectProductCodeLikeByVehicleByStock(String scenario,
                                                                              String productCode,
                                                                              List<String> vehicleModelCodeList,
                                                                              String stockPointCode) {
        if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return newProductStockPointService.selectProductCodeLikeByVehicleByStock(productCode, vehicleModelCodeList, stockPointCode);
    }

    @Override
    public List<NewProductStockPointVO> selectProductStockPointByIds(String scenario, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return newProductStockPointService.selectByPrimaryKeys(ids);
    }

    @Override
    public List<NewProductStockPointVO> selectAllProductStockPoint(String scenario) {
        return newProductStockPointService.selectAll();
    }

    @Override
    public Map<String, List<ResourceCalendarVO>> selectByStandardResourceIds(String scenario,
                                                                             List<String> standardResourceIds) {
        if (CollectionUtils.isEmpty(standardResourceIds)) {
            return new HashMap<>(6);
        }
        return resourceCalendarService.selectByStandardResourceIds(standardResourceIds);
    }

    @Override
    public List<ResourceCalendarVO> selectByPhysicalResourceIds(String scenario, List<String> physicalResourceIds) {
        if (CollectionUtils.isEmpty(physicalResourceIds)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return resourceCalendarService.selectByPhysicalResourceIds(physicalResourceIds);
    }

    @Override
    public List<ResourceCalendarVO> selectByResourceIdsAndDate(String scenario, ResourceCalendarQueryDTO calendarQueryDTO) {
        return resourceCalendarService.selectByParams(calendarQueryDTO.getStandardResourceIds(),
                calendarQueryDTO.getPhysicalResourceIds(), calendarQueryDTO.getCalendarType(),
                calendarQueryDTO.getStartDate(), calendarQueryDTO.getEndDate());
    }

    @Override
    public List<StandardResourceVO> selectStandardResourceVOS(String scenario) {
        return standardResourceService.selectAll();
    }

    @Override
    public List<StandardResourceVO> selectStandardResourceVOSByParams(String scenario, Map<String, Object> params) {
        return standardResourceService.selectByParams(params);
    }

    @Override
    public List<PhysicalResourceVO> selectPhysicalResourceByStandResourceIds(String scenario,
                                                                             List<String> standardResourceIds) {
        if (CollectionUtils.isEmpty(standardResourceIds)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return physicalResourceService.selectByStandResourceIds(standardResourceIds);
    }

    @Override
    public BaseResponse<Void> createByPhysicalResourceIds(String scenario, List<ResourceCalendarDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return BaseResponse.success();
        }
        // 删掉原来的数据
        List<String> physicalResourceIds =
                dtoList.stream().map(ResourceCalendarDTO::getPhysicalResourceId).distinct().collect(Collectors.toList());
        resourceCalendarDao.deleteByResourceIds(physicalResourceIds);
        List<ResourceCalendarPO> resourceCalendarPOS = ResourceCalendarConvertor.INSTANCE.dto2Pos(dtoList);
        BasePOUtils.insertBatchFiller(resourceCalendarPOS);
        resourceCalendarDao.insertBatchWithPrimaryKey(resourceCalendarPOS);
        return BaseResponse.success();
    }

    @Override
    public List<ResourceCalendarVO> selectResourceCalendarByParams(String scenario, Map<String, Object> params) {
        return resourceCalendarService.selectByParams(params);
    }

    @Override
    public List<RoutingVO> getRoutingByProductIdList(String scenario, List<String> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return routingService.getRoutingByProductIdList(productIdList);
    }

    @Override
    public List<RoutingStepVO> getRoutingStepByRoutingIds(String scenario, List<String> routingIds) {
        if (CollectionUtils.isEmpty(routingIds)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return routingStepService.selectByRoutingIds(routingIds);
    }

    @Override
    public List<NewStockPointVO> selectStockPointByParams(String scenario, Map<String, Object> params) {
        return newStockPointService.selectByParams(params);
    }

    @Override
    public List<NewStockPointVO> selectAllStockPoint(String scenario) {
        return newStockPointService.selectAll();
    }

    @Override
    public PlanningHorizonVO selectPlanningHorizon(String scenario) {
        return planningHorizonService.selectOne();
    }

    @Override
    public BaseResponse<Void> handleProductionOrganizes(String scenario, List<MesProductionOrganize> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        // 处理库存点
        List<NewStockPointVO> oldStockPointVOS = newStockPointService.selectAll();
        Map<String, NewStockPointVO> oldStockPointVOMap = oldStockPointVOS.stream().collect(
                Collectors.toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));
        List<NewStockPointDTO> insertNewStockPointDTOs = Lists.newArrayList();
        List<NewStockPointDTO> updateNewStockPointDTOs = Lists.newArrayList();
        for (MesProductionOrganize mesProductionOrganize : o) {
            String plantId = mesProductionOrganize.getPlantId();
            String enabled = "Y".equals(mesProductionOrganize.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            NewStockPointDTO newStockPointDTO = new NewStockPointDTO();
            if (oldStockPointVOMap.containsKey(plantId)) {
                // 更新库存点
                NewStockPointVO newStockPointVO = oldStockPointVOMap.get(plantId);
                BeanUtils.copyProperties(newStockPointVO, newStockPointDTO);
                newStockPointDTO.setStockPointCode(mesProductionOrganize.getPlantCode());
                newStockPointDTO.setStockPointName(mesProductionOrganize.getPlantDesc());
                newStockPointDTO.setOrganizeId(plantId);
                newStockPointDTO.setLastUpdateTime(mesProductionOrganize.getLastUpdateDate());
                newStockPointDTO.setPlanArea(mesProductionOrganize.getScheduleRegionCode());
                newStockPointDTO.setInterfaceSource("MES");
                newStockPointDTO.setEnabled(enabled);
                updateNewStockPointDTOs.add(newStockPointDTO);
            } else {
                // 新增销售组织
                newStockPointDTO.setId(plantId);
                newStockPointDTO.setStockPointCode(mesProductionOrganize.getPlantCode());
                newStockPointDTO.setStockPointName(mesProductionOrganize.getPlantDesc());
                newStockPointDTO.setOrganizeId(plantId);
                newStockPointDTO.setLastUpdateTime(mesProductionOrganize.getLastUpdateDate());
                newStockPointDTO.setPlanArea(mesProductionOrganize.getScheduleRegionCode());
                newStockPointDTO.setInterfaceSource("MES");
                newStockPointDTO.setEnabled(enabled);
                insertNewStockPointDTOs.add(newStockPointDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertNewStockPointDTOs)) {
            newStockPointService.doCreateBatch(insertNewStockPointDTOs);
        }
        if (CollectionUtils.isNotEmpty(updateNewStockPointDTOs)) {
            newStockPointService.doUpdateBatch(updateNewStockPointDTOs);
        }
        // 处理生产组织
        List<ProductionOrganizeVO> productionOrganizeVOS = productionOrganizeService.selectAll();
        Map<String, ProductionOrganizeVO> oldProductionOrganizeVOMap = productionOrganizeVOS.stream().collect(
                Collectors.toMap(ProductionOrganizeVO::getId, Function.identity(), (v1, v2) -> v1));
        List<ProductionOrganizeDTO> insertProductionOrganizeDTOs = Lists.newArrayList();
        List<ProductionOrganizeDTO> updateProductionOrganizeDTOs = Lists.newArrayList();
        for (MesProductionOrganize mesProductionOrganize : o) {
            String plantId = mesProductionOrganize.getPlantId();
            String enabled = "Y".equals(mesProductionOrganize.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                    YesOrNoEnum.NO.getCode();
            ProductionOrganizeDTO productionOrganizeDTO = new ProductionOrganizeDTO();
            if (oldStockPointVOMap.containsKey(plantId)) {
                // 更新生产组织
                ProductionOrganizeVO productionOrganizeVO = oldProductionOrganizeVOMap.get(plantId);
                if (productionOrganizeVO != null) {
                    BeanUtils.copyProperties(productionOrganizeVO, productionOrganizeDTO);
                    productionOrganizeDTO.setProductionOrgCode(mesProductionOrganize.getPlantCode());
                    productionOrganizeDTO.setProductionOrgName(mesProductionOrganize.getPlantDesc());
                    productionOrganizeDTO.setLastUpdateTime(mesProductionOrganize.getLastUpdateDate());
                    productionOrganizeDTO.setPlanArea(mesProductionOrganize.getScheduleRegionCode());
                    productionOrganizeDTO.setEnabled(enabled);
                    updateProductionOrganizeDTOs.add(productionOrganizeDTO);
                }
            } else {
                // 新增生产组织
                productionOrganizeDTO.setId(plantId);
                productionOrganizeDTO.setProductionOrgCode(mesProductionOrganize.getPlantCode());
                productionOrganizeDTO.setProductionOrgName(mesProductionOrganize.getPlantDesc());
                productionOrganizeDTO.setLastUpdateTime(mesProductionOrganize.getLastUpdateDate());
                productionOrganizeDTO.setPlanArea(mesProductionOrganize.getScheduleRegionCode());
                productionOrganizeDTO.setEnabled(enabled);
                insertProductionOrganizeDTOs.add(productionOrganizeDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertProductionOrganizeDTOs)) {
            productionOrganizeService.doCreateBatch(insertProductionOrganizeDTOs);
        }
        if (CollectionUtils.isNotEmpty(updateProductionOrganizeDTOs)) {
            productionOrganizeService.doUpdateBatch(updateProductionOrganizeDTOs);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleProductStockPoints(String scenario, List<ErpProduct> o) {
        return newProductStockPointService.sync(o);
    }

    @Override
    public BaseResponse<Void> handleProductStockPoints1(String scenario, List<MdmProductionTime> mdmProductionTimes) {
        return newProductStockPointService.syncProductTime(mdmProductionTimes);
    }

    @Override
    public List<LabelValue<String>> selectProductStockPointLabelValueByParams(String scenario,
                                                                              Map<String, Object> params) {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<NewProductStockPointVO> productStockPointVOS = newProductStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(productStockPointVOS)) {
            return result;
        }
        productStockPointVOS.forEach(x -> {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setLabel(x.getProductName());
            labelValue.setValue(x.getProductCode());
            result.add(labelValue);
        });
        return result;
    }

    @Override
    public List<NewProductStockPointVO> selectProductStockPointByStockPointCodes(String scenario,
                                                                                 List<String> stockPointCodes) {
        return newProductStockPointService.selectByStockPointCodes(stockPointCodes);
    }

    @Override
    public List<NewProductStockPointVO> selectByUserId(String scenario, String userId, List<String> stockPointCodeList) {
        return newProductStockPointService.selectByUserId(userId, stockPointCodeList);
    }

    @Override
    public Map<String, String> selectProductVehicleModel(String scenario, Map<String, Object> params) {
        Map<String, String> result = MapUtil.newHashMap();
        log.info("newProductStockPoint params :{}", JSONUtil.toJsonStr(params));
        List<NewProductStockPointVO> newProductStockPointVOS =
                newProductStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            return result;
        }
        result.putAll(newProductStockPointVOS.stream().filter(x -> StringUtils.isNotBlank(x.getProductCode())
                        && StringUtils.isNotBlank(x.getVehicleModelCode()))
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode,
                        NewProductStockPointVO::getVehicleModelCode,
                        (v1, v2) -> v1)));
        return result;
    }

    @Override
    public MdsAlgorithmInputNew getAlgorithmInputNewData(String scenario, List<String> productCodes) {
        return mdsAlgoInputService.getInput(scenario, productCodes);
    }

    @Override
    public AlgorithmPolymerization getAlgorithmPolymerization() {
        return mdsAlgoInputService.getAlgorithmPolymerization();
    }

    @Override
    public List<NewProductStockPointVO> selectByProductCode(String scenario, List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return newProductStockPointService.selectByProductCode(codeList);
    }

    @Override
    public List<ProductionOrganizeVO> selectProductionOrganizeByParams(String scenario, Map<String, Object> params) {
        return productionOrganizeService.selectByParams(params);
    }

    @Override
    public List<LabelValue<String>> selectProductCodeLikeByParams(String scenario, Map<String, Object> params) {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<NewProductStockPointVO> productStockPointVOS = newProductStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(productStockPointVOS)) {
            return result;
        }
        productStockPointVOS.forEach(x -> {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setLabel(x.getProductCode());
            labelValue.setValue(x.getProductCode());
            result.add(labelValue);
        });
        return result;
    }

    /**
     * 物料信息-同步过来需要做数据处理
     *
     * @param newProductStockPointDTO
     */
    private void parseProductCode(NewProductStockPointDTO newProductStockPointDTO) {
        String productClassify = newProductStockPointDTO.getProductClassify();
        String productCode = newProductStockPointDTO.getProductCode();
        try {
            if (productClassify.contains("RA.A")) {
                //原片
                newProductStockPointDTO.setProductCategory(NewProductEnum.ORIGINAL_FILM.getCode());
                //productCode从第4位开始截取四位
                if (productCode.length() >= 7) {

                    String productLength = productCode.substring(3, 7);
                    newProductStockPointDTO.setProductLength(BigDecimal.valueOf(Integer.parseInt(productLength)));

                }
                //productCode从第8位开始截取四位(宽度)
                if (productCode.length() >= 11) {

                    String productWidth = productCode.substring(7, 11);
                    newProductStockPointDTO.setProductWidth(BigDecimal.valueOf(Integer.parseInt(productWidth)));
                }
                //productCode从第13位开始截取2位(厚度)
                if (productCode.length() >= 14) {

                    String productThickness = productCode.substring(12, 14);
                    newProductStockPointDTO.setProductThickness(BigDecimal.valueOf(Integer.parseInt(productThickness)));
                }
                //productCode若只有16位取最后1位为颜色，编码超过16位的都从16位往后截取，都是颜色
                if (productCode.length() >= 16) {

                    String productColor = productCode.substring(15);
                    newProductStockPointDTO.setProductColor(productColor);
                }
            }
            if (productClassify.contains("C0.0")) {
                //毛坯
                //productCode前4位表示长度
                if (productCode.length() >= 4) {

                    String productLength = productCode.substring(0, 4);
                    newProductStockPointDTO.setProductLength(BigDecimal.valueOf(Integer.parseInt(productLength)));
                }
                //productCode第五位开始四位表示宽
                if (productCode.length() >= 8) {

                    String productWidth = productCode.substring(4, 8);
                    newProductStockPointDTO.setProductWidth(BigDecimal.valueOf(Integer.parseInt(productWidth)));
                }
                //productCode第9位开始四位表示厚度
                if (productCode.length() >= 12) {

                    String productThickness = productCode.substring(8, 12);
                    newProductStockPointDTO.setProductThickness(BigDecimal.valueOf(Integer.parseInt(productThickness)));
                }
                //productCode最后一位往前取，遇到数字停止截取，截取出来的为颜色
                String productColor = extractColor(productCode);
                newProductStockPointDTO.setProductColor(productColor);
            }
            if (productClassify.contains("RA.V")) {
                newProductStockPointDTO.setProductCategory(NewProductEnum.PVB.getCode());
            }
            if (productClassify.contains("BB") || productClassify.contains("BG") || productClassify.contains("BJ")) {
                newProductStockPointDTO.setProductCategory(NewProductEnum.B_TYPE.getCode());
            }

        } catch (Exception e) {
            log.error("解析productCode失败,物料分类为:{},物料编码为:{}", productClassify, productCode);
            log.error("解析失败原因;{}", e.getMessage());
        }
    }

    @Override
    public List<PhysicalResourceVO> selectAllPhysicalResource(String scenario) {
        return physicalResourceService.selectAll();
    }

    private String extractColor(String productCode) {
        if (productCode == null || productCode.isEmpty()) {
            return "";
        }

        StringBuilder color = new StringBuilder();
        boolean foundDigit = false;

        for (int i = productCode.length() - 1; i >= 0; i--) {
            char c = productCode.charAt(i);

            if (Character.isDigit(c)) {
                if (!foundDigit) {
                    foundDigit = true;
                } else {
                    break;
                }
            } else {
                color.insert(0, c);
            }
        }
        return color.toString();
    }

    @Override
    public List<ProductBoxRelationVO> selectProductBoxRelationVOByProductCodeList(String scenario, List<String> productCodeList) {
        return productBoxRelationService.selectVOByProductCodeList(productCodeList);
    }

    @Override
    public List<BoxInfoVO> selectBoxInfoVOByBoxIdList(String scenario, List<String> ids) {
        return boxInfoService.selectByPrimaryKeys(ids);
    }

    @Override
    public List<MdsProductStockPointBaseVO> selectProductStockPointBaseByParams(String scenario, Map<String, Object> params) {
        return mdsProductStockPointBaseService.selectByParams(params);
    }

    @Override
    public List<RoutingStepResourceVO> selectRoutingStepResourceByRoutingStepIds(String scenario, List<String> routingStepIds) {
        return routingStepResourceService.selectByRoutingStepIds(routingStepIds);
    }

    @Override
    public List<RoutingStepResourceVO> selectRoutingStepResourceByRoutingStepIds2(List<String> routingStepIds) {
        return routingStepResourceService.selectByParams(ImmutableMap.of("routingStepIds", routingStepIds, "enabled", YesOrNoEnum.YES.getCode()));
    }

    @Override
    public List<RoutingStepResourceVO> selectRoutingStepResourceByIds(String scenario, List<String> ids) {
        List<RoutingStepResourcePO> routingStepResourcePOS = routingStepResourceDao.selectByPrimaryKeys(ids);
        List<RoutingStepResourceVO> dataList = RoutingStepResourceConvertor.INSTANCE.po2Vos(routingStepResourcePOS);

        return routingStepResourceService.invocation(dataList, null, routingStepResourceService.getInvocationName());
    }

    @Override
    public List<ProductCandidateResourceTimeVO> selectProductCandidateResourceTimeByParams(String scenario, Map<String, Object> params) {
        return productCandidateResourceTimeDao.selectVOByParams(params);
    }

    @Override
    public List<StandardResourceVO> selectResourceAndOperation() {
        return productCandidateResourceTimeDao.selectResourceAndOperation();
    }

    @Override
    public List<ProductCandidateResourceTimeVO> selectProductCandidateResourceTimeByCandidateResourceId(String scenario, List<String> candidateResourceIdList) {
        return productCandidateResourceTimeDao.selectByCandidateResourceId(candidateResourceIdList);
    }

    @Override
    public void deleteProductCandidateResourceTimeByStartAndEndTime(Date startTime, Date endTime) {
        productCandidateResourceTimeDao.deleteByStartAndEndTime(startTime, endTime);
    }

    @Override
    public List<ProductCandidateResourceTimeVO> selectProductCandidateResourceTimeByOperationCodeAndEffectiveTime(String scenario, String operationCode, String effectiveTime) {
        return productCandidateResourceTimeDao.selectListByOperationCodeAndEffectiveTime(operationCode, effectiveTime);
    }

    @Override
    public void saveProductCandidateResourceTimeVO(String scenario, List<ProductCandidateResourceTimeVO> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<ProductCandidateResourceTimePO> insertList;
        List<ProductCandidateResourceTimePO> updateList;
        List<ProductCandidateResourceTimePO> productCandidateResourceTimePOS = ProductCandidateResourceTimeConvertor.INSTANCE.vo2Pos(vos);
        insertList = productCandidateResourceTimePOS.stream().filter(t -> com.yhl.platform.common.utils.StringUtils.isEmpty(t.getId())).collect(Collectors.toList());
        updateList = productCandidateResourceTimePOS.stream().filter(t -> com.yhl.platform.common.utils.StringUtils.isNotEmpty(t.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList);
            log.info("产品资源生产关系新增数据条数：{}", insertList.size());
            List<List<ProductCandidateResourceTimePO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(insertList, 2000);
            for (List<ProductCandidateResourceTimePO> list : lists) {
                productCandidateResourceTimeDao.insertBatchWithPrimaryKey(list);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            BasePOUtils.updateBatchFiller(updateList);
            log.info("产品资源生产关系更新数据条数：{}", updateList.size());
            List<List<ProductCandidateResourceTimePO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(updateList, 2000);
            for (List<ProductCandidateResourceTimePO> list : lists) {
                productCandidateResourceTimeDao.updateBatch(list);
            }
        }
    }

    @Override
    public List<ProductionOrganizationVO> selectOrganizationByPrimaryIds(String scenario, List<String> ids) {
        return productionOrganizationService.selectByPrimaryIds(ids);
    }

    @Override
    public List<PhysicalResourceVO> selectByPhysicalIds(String scenario, List<String> ids) {
        return physicalResourceService.selectByIds(ids);
    }

    @Override
    public List<RoutingStepInputVO> selectInputByRoutingStepIds(String scenario, List<String> routingStepIds) {
        return routingStepInputService.selectByRoutingStepIds(routingStepIds);
    }

    @Override
    public List<RoutingStepOutputVO> selectOutputByRoutingStepIds(String scenario, List<String> routingStepIds) {
        return routingStepOutputService.selectByRoutingStepIds(routingStepIds);
    }

    @Override
    public List<RoutingStepInputVO> selectByParamsRoutingStepInput(String scenario, Map<String, Object> params) {
        return routingStepInputService.selectByParams(params);
    }

    @Override
    public List<BomTreeNewVO> selectRoutingByInputProductIds(String scenario, BomTreeNewVO vo) {
        return bomTreeNewDao.selectRoutingByInputProductIds(vo.getInputProductIdList(), vo.getProductType());
    }

    @Override
    public List<ProductBoxRelationVO> selectAllBox(String scenario) {
        return productBoxRelationService.selectAll();
    }

    @Override
    public List<RoutingStepVO> getRoutingStepByRoutingStepIds(String mdsScenario, List<String> routingStepId) {
        List<RoutingStepPO> routingStepPOS = routeStepDao.selectByPrimaryKeys(routingStepId);
        return RoutingStepConvertor.INSTANCE.po2Vos(routingStepPOS);
    }

    @Override
    public List<StandardStepVO> selectStandardStepAll(String scenario) {
        return standardStepService.selectAll();
    }

    @Override
    public List<RoutingStepVO> selectAllRoutingStep(String scenario) {
        return routingStepService.selectAll();
    }


    @Override
    public ProductDemandSupplyCalculateVO selectRoutingStepInputForSupplyCalculate(String scenario, String productCode,
                                                                                   String productId, List<Integer> sequenceNos) {
        return newRoutingStepInputService.selectRoutingStepInputForSupplyCalculate(scenario, productCode, productId, sequenceNos);
    }


    @Override
    public List<String> selectAllProductIdsFromBom() {
        return routingStepInputDao.selectAllProductIdsFromBom();
    }

    @Override
    public List<NewProductStockPointVO> selectTwoProductStockPoint(String scenario) {
        return newProductStockPointService.selectTwoData();
    }

    @Override
    public BaseResponse<Void> handleFoundationBoxInfo(String scenario, List<MesBoxInfo> o, String ebsOuId) {
        return boxInfoService.handleFoundationBoxInfo(o, ebsOuId);
    }


    @Override
    public List<RoutingVO> selectRoutingByRoutingIds(List<String> routingIds) {
        return routingService.selectByParams(ImmutableMap.of("ids", routingIds));
    }

    @Override
    public List<RoutingVO> selectRoutingByScenarioRoutingIds(String scenario, List<String> routingIds) {
        return routingService.selectByParams(ImmutableMap.of("ids", routingIds));
    }

    @Override
    public List<RoutingVO> selectRoutingByParams(String scenario, Map<String, Object> params) {
        return routingService.selectByParams(params);
    }

    @Override
    public List<RoutingStepVO> getAllRoutingStep() {
        return routingStepService.selectAll();
    }

    @Override
    public List<RoutingStepInputVO> getAllRoutingStepInput() {
        return routingStepInputService.selectAll();
    }

    @Override
    public List<RoutingStepOutputVO> getAllRoutingStepOutput() {
        return routingStepOutputService.selectAll();
    }

    @Override
    public List<ProductionOrganizationVO> selectProductionOrganizeAll() {
        return productionOrganizationService.selectAll();
    }

    @Override
    public List<BoxInfoVO> selectPiecePerBoxByBoxCode(String scenario, Map<String, Object> params) {
        return boxInfoService.selectByParams(params);
    }

    @Override
    public List<RoutingVO> getAllRouting() {
        return routingService.selectAll();
    }

    @Override
    public BaseResponse<Void> handleProductBoxRelation(String scenario, List<MesProductBoxRelation> o) {
        return productBoxRelationService.handleProductBoxRelation(o);
    }


    @Override
    public List<ProductSubstitutionRelationshipVO> getProductSubstitutionRelationship() {
        return productSubstitutionRelationshipService.selectAll();
    }

    @Override
    public List<ProductSubstitutionRelationshipVO> getProductSubstitutionRelationshipVOByParams(String scenario, Map<String, Object> params) {
        return productSubstitutionRelationshipService.selectByParams(params);
    }


    @Override
    public List<NewProductionOrganizationVO> selectNewProductionOrganizationVO(String scenario, Map<String, Object> params) {
        return newProductionOrganizationService.selectByParams(params);
    }

    @Override
    public List<String> doResourceCalendarForFeedback(String scenario, CalendarForFeedbackDTO calendarForFeedbackDTO) {
        List<String> returnList = new ArrayList<>();
        String onlyDeleteCalendarRuleIds = calendarForFeedbackDTO.getOnlyDeleteCalendarRuleIds();
        if (StringUtils.isNotEmpty(onlyDeleteCalendarRuleIds)) {
            calendarRuleDao.deleteBatch(Arrays.asList(onlyDeleteCalendarRuleIds.split(",")));
        } else {
            //1.获取异常班次，查看班次是否已经创建
            Map<String, String> shiftMap = getShiftMap("ABNORMAL");
            //删除对应的日历规则
            String calendarRuleIds = calendarForFeedbackDTO.getCalendarRuleIds();
            if (StringUtils.isNoneEmpty(calendarRuleIds)) {
                calendarRuleDao.deleteBatch(Arrays.asList(calendarRuleIds.split(",")));
            }
            //获取生产组织数据信息
            List<NewProductionOrganizationVO> orgList = newProductionOrganizationService
                    .selectByParams(MapUtil.of("organizationCode", calendarForFeedbackDTO.getOrganizationCode()));
            String organizationId = orgList.get(0).getId();
            //获取物理资源数据信息
            List<PhysicalResourceVO> physicalResourceList = physicalResourceService
                    .selectByParams(MapUtil.of("physicalResourceCode", calendarForFeedbackDTO.getResourceCode()));
            PhysicalResourceVO physicalResourceVO = physicalResourceList.get(0);
            common(calendarForFeedbackDTO, organizationId, physicalResourceVO.getStandardResourceId(), physicalResourceVO.getId(), returnList, shiftMap, "ABNORMAL");
        }
        //异步删除多余班次数据及刷新资源日历
        imdbAsync.deletionOfRedundantShifts();
        return returnList;
    }


    @Override
    public List<String> doResourceCalendarForCoatingMaintenanceSetting(String scenario, CalendarForFeedbackDTO calendarForFeedbackDTO) {
        List<String> returnList = new ArrayList<>();
        String onlyDeleteCalendarRuleIds = calendarForFeedbackDTO.getOnlyDeleteCalendarRuleIds();
        if (StringUtils.isNotEmpty(onlyDeleteCalendarRuleIds)) {
            calendarRuleService.doDelete(Arrays.asList(onlyDeleteCalendarRuleIds.split(",")));
        } else {
            Map<String, String> shiftMap = getShiftMap("OVERHAUL");
            //删除对应的日历规则
            String calendarRuleIds = calendarForFeedbackDTO.getCalendarRuleIds();
            if (StringUtils.isNoneEmpty(calendarRuleIds)) {
                calendarRuleService.doDelete(Arrays.asList(calendarRuleIds.split(",")));
            }
            PhysicalResourceVO physicalResourceVO = physicalResourceService.selectByPrimaryKey(calendarForFeedbackDTO.getResourceCode());
            if (physicalResourceVO == null) {
                throw new BusinessException("物理资源不存在");
            }
            StandardResourceVO standardResourceVO = standardResourceService.selectByPrimaryKey(physicalResourceVO.getStandardResourceId());
            if (standardResourceVO == null || StringUtils.isEmpty(standardResourceVO.getOrganizationId())) {
                throw new BusinessException("资源组不存在");
            }
            common(calendarForFeedbackDTO, standardResourceVO.getOrganizationId(), standardResourceVO.getId(), physicalResourceVO.getId(), returnList, shiftMap, "OVERHAUL");
        }
        //异步删除多余班次数据及刷新资源日历
        imdbAsync.deletionOfRedundantShifts();
        return returnList;
    }

    /**
     * 创建日历公共逻辑
     *
     * @param calendarForFeedbackDTO
     * @param organizationId
     * @param standardResourceId
     * @param physicalResourceId
     * @param returnList
     * @param shiftMap
     */
    private void common(CalendarForFeedbackDTO calendarForFeedbackDTO, String organizationId, String standardResourceId, String physicalResourceId, List<String> returnList, Map<String, String> shiftMap, String shiftType) {
        List<String> physicalResourceIds = Arrays.asList(physicalResourceId);
        List<String> standardResourceIds = Arrays.asList(standardResourceId);
        //2.根据异常日历的开始结束时间，维护对应的班次时间数据，日历规则数据
        Date startTime = calendarForFeedbackDTO.getStartTime();
        Date endTime = calendarForFeedbackDTO.getEndTime();
        List<Date> intervalDates = DateUtils.getIntervalDates(startTime, endTime);
        if (intervalDates.size() == 1) {
            //添加班次和日历规则
            doAddShiftAndCalendarRule(returnList, shiftMap, organizationId, physicalResourceIds, standardResourceIds,
                    startTime, endTime, 1, shiftType);
        } else if (intervalDates.size() == 2) {
            doAddShiftAndCalendarRule(returnList, shiftMap, organizationId, physicalResourceIds, standardResourceIds,
                    startTime, DateUtils.getDayLastTime(startTime), 1, shiftType);
            doAddShiftAndCalendarRule(returnList, shiftMap, organizationId, physicalResourceIds, standardResourceIds,
                    DateUtils.getDayFirstTime(endTime), endTime, 2, shiftType);
        } else {
            //中间跨去全天
            doAddShiftAndCalendarRule(returnList, shiftMap, organizationId, physicalResourceIds, standardResourceIds,
                    startTime, DateUtils.getDayLastTime(startTime), 1, shiftType);
            doAddShiftAndCalendarRule(returnList, shiftMap, organizationId, physicalResourceIds, standardResourceIds,
                    DateUtils.getDayFirstTime(intervalDates.get(1)), DateUtils.getDayLastTime(intervalDates.get(intervalDates.size() - 2)), 2, shiftType);
            doAddShiftAndCalendarRule(returnList, shiftMap, organizationId, physicalResourceIds, standardResourceIds,
                    DateUtils.getDayFirstTime(endTime), endTime, 3, shiftType);
        }
    }

    /**
     * 获取已有班次数据
     *
     * @param OVERHAUL
     * @return
     */
    private Map<String, String> getShiftMap(String OVERHAUL) {
        //1.获取异常班次，查看班次是否已经创建
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("enabled", YesOrNoEnum.YES.getCode());
        paramMap.put("shiftType", OVERHAUL);
        List<ShiftVO> shiftList = shiftService.selectByParams(paramMap);
        Map<String, String> shiftMap = shiftList.stream().collect(Collectors.toMap(ShiftVO::getShiftPattern, ShiftVO::getId));
        return shiftMap;
    }

    @Override
    public BaseResponse syncProductAboutBomData(String scenario, List<ProductAboutBomDTO> list) {
        return productAboutBomService.syncProductAboutBomData(scenario, list);
    }

    /**
     * 添加班次和日历规则
     *
     * @param returnList
     * @param shiftMap
     * @param organizationId
     * @param physicalResourceIds
     * @param standardResourceIds
     * @param startTime
     * @param endTime
     * @param sortNo
     */
    private void doAddShiftAndCalendarRule(List<String> returnList, Map<String, String> shiftMap, String organizationId,
                                           List<String> physicalResourceIds, List<String> standardResourceIds, Date startTime, Date endTime, Integer sortNo, String shiftType) {
        //同一天,出勤时间段
        String shiftPattern = DateUtils.dateToString(startTime, DateUtils.COMMON_TIME_STR1)
                + "-" + DateUtils.dateToString(endTime, DateUtils.COMMON_TIME_STR1);
        String shiftId = shiftMap.get(shiftPattern);
        if (StringUtils.isEmpty(shiftId)) {
            String shiftName = "ABNORMAL".equals(shiftType) ? "异常班次" : "镀膜维保班次";
            //新增班次，维护对应的资源日历
            ShiftDTO addShiftDTO = ShiftDTO.builder()
                    .shiftName(shiftPattern + shiftName)
                    .shiftType(shiftType)
                    .shiftPattern(shiftPattern)
                    .build();
            shiftService.doCreate(addShiftDTO);
            Map<String, Object> params = new HashMap<>(4);
            params.put("shiftName", addShiftDTO.getShiftName());
            List<ShiftVO> currentShiftList = shiftService.selectByParams(params);
            shiftId = currentShiftList.get(0).getId();
        }
        //维护班次对应的日历规则
        CalendarRuleDTO calendarRuleDTO = CalendarRuleDTO.builder()
                .organizationId(organizationId)//生产组织id
                .standardResourceIds(standardResourceIds)
                .physicalResourceIds(physicalResourceIds)
                .shiftIds(Arrays.asList(shiftId))
                .ruleName("异常日历规则" + DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4) + "-" + sortNo)
                .repeatFrequency("WEEKLY")
                .frequencyPattern("1,2,3,4,5,6,7")
                .calendarRuleType(CalendarRuleTypeEnum.RESOURCE_TYPE.getCode())
                .efficiency(BigDecimal.ONE)
                .startDate(DateUtils.getDayFirstTime(startTime))
                .endDate(DateUtils.getDayFirstTime(endTime))
                .build();
        CalendarRuleDO calendarRuleDO = CalendarRuleConvertor.INSTANCE.dto2Do(calendarRuleDTO);
        calendarRuleDomainService.formatData(calendarRuleDO);
        calendarRuleDomainService.validation(calendarRuleDO);
        CalendarRulePO calendarRulePO = CalendarRuleConvertor.INSTANCE.do2Po(calendarRuleDO);
        BasePOUtils.insertFiller(calendarRulePO);
        calendarRuleDao.insert(calendarRulePO);
        returnList.add(calendarRulePO.getId());
    }

    @Override
    public BaseResponse<Void> syncProductSubstitutionData(String scenario, List<ProductSubstitutionRelationshipDTO> list, String organizationCodes) {
        return productSubstitutionRelationshipService.syncProductSubstitutionData(scenario, list, organizationCodes);
    }

    @Override
    public BaseResponse<Void> syncProductStockPoints(String scenario, String tenantId) {
        return newProductStockPointService.syncProductStockPoints(tenantId, null);
    }

    @Override
    public BaseResponse<Void> synBoxInfo(String scenario, Map<String, Object> params) {
        return boxInfoService.syncStockPoints(params.get("tenantId").toString(), params.get("ebsOuId").toString());
    }

    @Override
    public BaseResponse<Void> synProductBoxRelation(String scenario, String tenantId) {
        return productBoxRelationService.syncProductBoxRelation(tenantId);
    }

    @Override
    public BaseResponse<Void> synStockPoint(String scenario, String tenantId) {
        return newStockPointService.syncStockPoints(tenantId);
    }

    @Override
    public BaseResponse<Void> syncProductionTime(String scenario, String tenantId) {
        return newProductStockPointService.syncProductionTime(tenantId);
    }

    @Override
    public List<NewStockPointVO> selectSaleOrganize(String scenario) {
        return newStockPointService.selectSaleOrgaByOrganizeType();
    }

    @Override
    public BaseResponse<Void> syncProductStockPointBase(String scenario, List<ErpProductStockPointBase> productStockPointBases) {
        return mdsProductStockPointBaseService.syncProductStockPointBase(scenario, productStockPointBases);
    }

    @Override
    public List<NewStockPointVO> selectNewStockPointVOByParams(String scenario, Map<String, Object> params) {
        return newStockPointService.selectByParams(params);
    }


    @Override
    public List<String> selectAllVehicleModel(String scenario) {
        return newStockPointService.selectAllVehicleModel();
    }

    @Override
    public List<NewProductStockPointVO> selectFinishedProductByInputProductId(String scenario, String inputProductId) {
        List<String> productIds = Lists.newArrayList();
        List<String> inputProductIds = Lists.newArrayList();
        inputProductIds.add(inputProductId);
        Boolean firstLoop = true;
        while (CollectionUtils.isNotEmpty(inputProductIds)) {
            //1.查询有这个物料的BOM对应的工艺
            List<RoutingStepInputVO> routingStepInputList = routingStepInputNewService
                    .selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                            "inputProductIds", inputProductIds));
            //不是首次查，维护成品物料ID，并进行递归查询
            if (!firstLoop) {
                if (CollectionUtils.isEmpty(routingStepInputList)) {
                    //全是成品，递归中止
                    productIds.addAll(inputProductIds);
                    inputProductIds = Lists.newArrayList();
                    continue;
                } else {
                    //添加完工
                    List<String> currentPorductIds = routingStepInputList.stream()
                            .map(RoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
                    List<String> finishPorductIds = inputProductIds;
                    finishPorductIds.removeAll(currentPorductIds);
                    productIds.addAll(finishPorductIds);
                    inputProductIds = currentPorductIds;
                }
            }
            List<String> routingIds = routingStepInputList.stream().map(RoutingStepInputVO::getRoutingId)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(routingIds)) {
                inputProductIds = Lists.newArrayList();
                continue;
            }
            //2.查询所有的工艺
            List<RoutingVO> routingInfos = routingService.selectByParams(ImmutableMap.of("ids", routingIds,
                    "enabled", YesOrNoEnum.YES.getCode()));
            if (CollectionUtils.isEmpty(routingInfos)) {
                inputProductIds = Lists.newArrayList();
                continue;
            }
            List<String> routingProductIds = routingInfos.stream().map(RoutingVO::getProductId).distinct()
                    .collect(Collectors.toList());
            inputProductIds.clear();
            inputProductIds.addAll(routingProductIds);
            firstLoop = false;

        }
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        //查询物料数据信息
        return newProductStockPointService.selectByPrimaryKeys(productIds);
    }

    @Override
    public BaseResponse<Void> handleRoutingStepResource(String scenario, List<MesMoldChangeTime> o) {
        return productCandidateResourceTimeService.syncRoutingStepResourceBase(o);
    }

    @Override
    public List<SupplierVO> selectSupplierByParams(String scenario, Map<String, Object> params) {
        return supplierService.selectByParams(params);
    }

    @Override
    public List<SupplierPO> selectSupplierPoByParams(String scenario, Map<String, Object> params) {
        return supplierDataDao.selectByParams(params);
    }

    @Override
    public SupplierPO selectSupplierByPrimaryKey(String scenario, String supplierId) {
        return supplierDataDao.selectByPrimaryKey(supplierId);
    }

    @Override
    public List<SupplierVO> selectSupplierByPrimaryKeys(List<String> supplierIds) {
        List<SupplierPO> supplierList = supplierDataDao.selectByPrimaryKeys(supplierIds);
        return SupplierConvertor.INSTANCE.po2Vos(supplierList);
    }

    @Override
    public BaseResponse<Void> supplierUpdate(String scenario, SupplierPO o) {
        supplierDataDao.update(o);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> supplierAddSupplierColumn(String scenario, SupplierPurchaseRationMap map) {
        List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = map.getSupplierPurchaseRatioVOList();
        HashMap<String, Object> params = map.getMap();
        supplierService.addSupplierColumn(supplierPurchaseRatioVOList, params.get("invocation").toString(), params.get("setSupplier").toString(), params.get("ids").toString(), params.get("supplierId").toString(), params.get("id").toString());
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> handleSupplier(String scenario, List<ErpSupplier> erpSuppliers) {
        BaseResponse<Void> voidBaseResponse = supplierService.handleSupplier(erpSuppliers);
        return voidBaseResponse;
    }

    @Override
    public List<NewProductStockPointVO> selectByLoadPosition(String scenario, List<String> accessPositionList) {
        if (CollectionUtils.isEmpty(accessPositionList)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return newProductStockPointService.selectByLoadPosition(accessPositionList);
    }

    @Override
    public List<ProductBoxRelationVO> selectProductBoxRelationByProductStockPointId(String scenario, List<String> productStockPointIdList) {
        if (CollectionUtils.isNotEmpty(productStockPointIdList)) {
            return productBoxRelationService.selectProductBoxRelationByProductStockPointId(productStockPointIdList);
        }
        return Collections.emptyList();
    }

    @Override
    public BaseResponse<Void> handleProductQualifiedSupplier(String scenario, List<MesProductQualifiedSupplier> o) {
        return supplierOwnerInfoService.sync(o);
    }

    @Override
    public BaseResponse<Void> handleProductRoutings(String scenario, List<ErpProductRouting> o) {
        return productRoutingService.doSync(o, scenario);
    }

    @Override
    public BaseResponse<Void> handleDeleteGroups(String data, List<ErpDeleteGroup> o) {
        return mdsDeleteGroupsService.sync(o);
    }


    @Override
    public BaseResponse<Void> handleOpYield(String data, List<MesOpYield> o) {
        return mdsOpYieldService.sync(o);
    }

    @Override
    public List<PlanHorizonInputNewData> selectTimeSequence() {
        List<PlanHorizonInputNewData> result = new ArrayList<>();
        String decisionType = DecisionTypeEnum.LIMITED_CAPACITY_COMPUTING.getCode();
        List<TimePeriodGroupVO> timePeriodGroupVOS = timePeriodGroupService.selectByParams(ImmutableMap
                .of("planningPeriodGroup", YesOrNoEnum.YES.getCode(), "decisionType", decisionType));
        List<String> groupIds = timePeriodGroupVOS.stream().map(TimePeriodGroupVO::getId).collect(Collectors.toList());
        List<TimePeriodVO> timePeriods = timePeriodService.selectByParams(ImmutableMap.of("timePeriodGroupIds", groupIds))
                .stream().sorted(Comparator.comparing(TimePeriodVO::getPeriodStart)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timePeriods)) {
            return ListUtil.empty();
        }
        for (int i = 0; i < timePeriods.size(); i++) {
            TimePeriodVO timePeriodVO = timePeriods.get(i);
            Date periodStart = timePeriodVO.getPeriodStart();
            Date periodEnd = timePeriodVO.getPeriodEnd();
            // if (periodStart.equals(periodEnd)) {
            periodEnd = DateUtils.moveDay(periodEnd, 1);
            // }
            PlanHorizonInputNewData planHorizonInputData = PlanHorizonInputNewData.builder()
                    .periodId(timePeriodVO.getId())
                    .periodSequence(i)
                    .periodStartTime(DateUtils.dateToString(periodStart, DateUtils.COMMON_DATE_STR3))
                    .periodEndTime(DateUtils.dateToString(periodEnd, DateUtils.COMMON_DATE_STR3))
                    .build();
            result.add(planHorizonInputData);
        }
        return result;
    }

    @Override
    public List<RuleEncodingsVO> getRuleEncoding() {
        return ruleEncodingsService.selectAll();
    }

    @Override
    public List<String> selectVehiclesByProductCode(String scenario, List<String> codeList) {
        return newProductStockPointService.selectVehiclesByProductCode(codeList);
    }

    @Override
    public List<RoutingDO> getRoutingDOByParams(Map<String, Object> params) {
        return mdsWorkOrderSyncRoutingService.getRoutingDOByParams(params);
    }

    @Override
    public void selectiveUpdateRuleEncodings(RuleEncodingsVO ruleEncodingsVO) {
        RuleEncodingsDTO ruleEncodingsDTO = RuleEncodingsConvertor.INSTANCE.vo2dto(ruleEncodingsVO);
        ruleEncodingsService.doUpdateSelective(ruleEncodingsDTO);
    }

    @Override
    public List<NewProductStockPointVO> selectProduct4LoadingDemandSubmission(String scenario) {
        return newProductStockPointService.selectProduct4LoadingDemandSubmission();
    }


    @Override
    public List<PlanCancellationParamVO> selectCancellationParam(Map<String, Object> params) {
        return planCancellationParamService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> syncStandardResourceData(String scenario, List<StandardResourceDTO> list) {
        return fyStandardResourceService.syncStandardResourceData(scenario, list);
    }

    @Override
    public BaseResponse<Void> completeStandardResourceData(String scenario) {
        return fyStandardResourceService.completeStandardResourceData(scenario);
    }

    @Override
    public BaseResponse<Void> syncPhysicalResourceData(String scenario, List<PhysicalResourceLogDTO> list) {
        return physicalResourceLogService.syncPhysicalResourceLogData(list);
    }

    @Override
    public BaseResponse<Void> completePhysicalResourceData(String scenario) {
        return physicalResourceLogService.completePhysicalResourceData();
    }

    @Override
    public BaseResponse<Void> handleMoldTooling(String scenario, List<MesMoldTooling> o) {
        return moldToolingService.handleMoldTooling(o);
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroup(String scenario, List<MesMoldToolingGroup> o) {
        return moldToolingGroupService.handleMoldToolingGroup(o);
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroupDir(String scenario, List<MesMoldToolingGroupDir> o) {
        return newProductCandidateResourceService.handleMoldToolingGroupDir(o);
    }

    @Override
    public BaseResponse<Void> handleCustomer(String scenario, List<ErpCustomer> o) {
        return customerService.handleCustomer(o);
    }

    @Override
    public List<ProductCandidateResourceVO> selectProductCandidateResourceByParams(String scenario,
                                                                                   Map<String, Object> params) {
        return newProductCandidateResourceService.selectByParams(params);
    }

    @Override
    public List<ProductCandidateResourceVO> selectProductCandidateResourceVOByParams(Map<String, Object> params) {
        return newProductCandidateResourceService.selectVOByParams(params);
    }

    @Override
    public List<ProductCandidateResourceVO> selectMoldQuantityLimit() {
        return newProductCandidateResourceDao.selectMoldQuantityLimit();
    }

    @Override
    public Map<String, List<ProductRiskLevelVO>> selectProductRiskLevelMap(String scenario,
                                                                           List<String> productCodeList) {
        return newRoutingService.selectProductRiskLevelMap(productCodeList);
    }

    @Override
    public List<MdsOverDeadlineDaysVO> selectOverDeadlineDaysByParams(String scenario, Map<String, Object> params) {
        return mdsOverDeadlineDaysService.selectByParams(params);
    }

    @Override
    public List<NewProductStockPointVO> selectSrmByStockCodeAndProductCode(String scenario, List<SrmSupplierPurchase> values) {
        return newProductStockPointService.selectSrmByStockCodeAndProductCode(values);
    }

    @Override
    public List<CustomerVO> selectCustomerByParams1(String scenario, HashMap<String, Object> map) {
        return customerService.selectByParams(map);
    }


    @Override
    public List<CustomerVO> selectCustomerByParams(HashMap<String, Object> customerMap) {
        return customerService.selectByParams(customerMap);
    }

    @Override
    public List<NewProductStockPointVO> selectProductStockPointByProducts(List<String> productCodes) {
        return newProductStockPointService.selectByProductCode(productCodes);
    }

    @Override
    public List<NewStockPointVO> selectStockPointBySubCategory(HashMap<String, Object> subCategoryMap) {
        return newStockPointService.selectByParams(subCategoryMap);
    }

    @Override
    public List<BomRoutingStepInputVO> selectBomRoutingStepInputByParams(String scenario, Map<String, Object> params) {
        return bomTreeNewDao.selectRoutingStepInputByParams(params);
    }

    @Override
    public List<ProductBoxRelationVO> selectProductBoxRelationByProductStockPointIds(String scenario,
                                                                                     List<String> productStockPointIds) {
        if (CollectionUtils.isNotEmpty(productStockPointIds)) {
            return productBoxRelationService.selectByParams(ImmutableMap.of(
                    "enabled", YesOrNoEnum.YES.getCode(),
                    "productStockPointIdList", productStockPointIds));
        }
        return Collections.emptyList();
    }

    @Override
    public List<PhysicalResourceVO> selectPhysicalResourceByParams(String scenario, Map<String, Object> params) {
        return physicalResourceDao.selectVOByParams(params);
    }

    @Override
    public List<String> getPlannerProduct(String scenario, String userId) {
        return mdsProductStockPointBaseDao.selectPermissionsProductCodeByLineGroup(userId);
    }

    @Override
    public List<String> selectPermissionsByLineGroupList(String scenario, List<String> lineGroups) {
        return mdsProductStockPointBaseDao.selectPermissionsByLineGroupList(lineGroups);
    }

    @Override
    public List<String> getPlannerStandardResource(String userId) {
        return mdsProductStockPointBaseDao.selectPermissionsByLineGroup(userId);
    }

    @Override
    public List<PhysicalResourceVO> getPlannerPhysicalResource(String userId) {
        List<StandardResourceVO> standardResources = standardResourceService.selectByParams(ImmutableMap.of("productionPlanner", userId));
        Map<String, StandardResourceVO> standardResourceMap = standardResources.stream()
                .collect(Collectors.toMap(StandardResourceVO::getId, Function.identity()));
        return physicalResourceService.selectByStandResourceIds(
                        standardResources.stream().map(StandardResourceVO::getId).collect(Collectors.toList()))
                .stream()
                .peek(physicalResource -> {
                    StandardResourceVO standardResource = standardResourceMap.get(physicalResource.getStandardResourceId());
                    if (standardResource != null) {
                        physicalResource.setStandardResourceCode(standardResource.getStandardResourceCode());
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public void doUpdateRoutingStep(List<RoutingStepDTO> routingStepVOList) {
        com.google.common.collect.Lists.partition(routingStepVOList, 2000).forEach(p -> {
            routingStepService.doUpdateBatch(p);
        });
    }

    @Override
    public List<SupplierAddressVO> selectSupplierAddressByParams(String scenario, Map<String, Object> params) {
        return supplierAddressService.selectVOByParams(params);
    }

    @Override
    public List<ProductBomVO> selectProductBomVOByParams(String scenario, Map<String, Object> params) {
        return mdsProductBomService.selectVOByParams(params);
    }

    @Override
    public List<ProductBomVersionVO> selectProductBomVersionVOByParams(String scenario, Map<String, Object> params) {
        return mdsProductBomVersionService.selectVOByParams(params);
    }

    @Override
    public Map<String, String> selectByLineGroup(List<String> lineGroupList) {
        return mdsProductStockPointBaseService.selectByLineGroup(lineGroupList);
    }

    @Override
    public List<String> getYpProductCodes(String scenario) {
        return newProductStockPointService.selectYpProductCodes();
    }

    @Override
    public List<RoutingVO> selectRoutingByParamOnDynamicColumns(String scenario, FeignDynamicParam feignDynamicParam) {
        return routingService.selectRoutingByParamOnDynamicColumns(feignDynamicParam.getDynamicColumnParam(), feignDynamicParam.getQueryParam());
    }

    @Override
    public List<RoutingStepVO> selectRoutingStepByParamOnDynamicColumns(String scenario, FeignDynamicParam feignDynamicParam) {
        return routingStepService.selectRoutingStepByParamOnDynamicColumns(feignDynamicParam.getDynamicColumnParam(), feignDynamicParam.getQueryParam());
    }

    @Override
    public List<RoutingStepInputVO> selectRoutingStepInputByParamOnDynamicColumns(String scenario, FeignDynamicParam feignDynamicParam) {
        return routingStepInputService.selectRoutingStepInputByParamOnDynamicColumns(feignDynamicParam.getDynamicColumnParam(), feignDynamicParam.getQueryParam());
    }

    @Override
    public List<NewRoutingStepInputVO> selectFormingProcess(String scenario, List<String> inputProductIds) {
        return newRoutingStepInputService.selectFormingProcess(inputProductIds);
    }

    @Override
    public List<StandardStepVO> selectDirectFormingProcess(String scenario, String inputProductId) {
        return newRoutingStepInputService.selectDirectFormingProcess(inputProductId);
    }

    @Override
    public Map<String, String> checkPorductRouting(String scenario, List<String> productCodes) {
        return newProductStockPointService.checkPorductRouting(productCodes);
    }

    @Override
    public List<String> getPlannerProductIdList(String creator) {
        return mdsProductStockPointBaseDao.getPlannerProductIdList(creator);
    }

    @Override
    public List<String> getPlannerProductIdByLineGroup(List<String> lineGroupList) {
        return mdsProductStockPointBaseDao.getPlannerProductIdByLineGroup(lineGroupList);
    }

    @Override
    public StandardResourceVO getStandResourceByPhysicalId(String scenario, String physicalResourceId) {
        List<String> ids = Collections.singletonList(physicalResourceId);
        List<PhysicalResourceVO> physicalResourceVOS = physicalResourceService.selectByIds(ids);
        if (physicalResourceVOS.isEmpty()) {
            return null;
        } else {
            String standardResourceId = physicalResourceVOS.get(0).getStandardResourceId();
            return standardResourceService.selectByPrimaryKey(standardResourceId);
        }
    }

    @Override
    public List<StandardResourceVO> selectStandardByPhysicalCodes(String scenario, List<String> collect) {
        Map<String, Object> map = MapUtils.newHashMap();
        map.put("physicalResourceCodes", collect);
        List<PhysicalResourceVO> physicalResourceVOS = physicalResourceService.selectByParams(map);
        if (physicalResourceVOS.isEmpty()) {
            return ListUtils.newArrayList();
        } else {
            List<String> standardIds = physicalResourceVOS.stream().map(PhysicalResourceVO::getStandardResourceId).collect(Collectors.toList());
            return standardResourceService.selectByPrimaryKeys(standardIds);
        }
    }

    @Override
    public BaseResponse<Void> handleCuringTime(String scenario, List<MesCuringTime> mesCuringTimes) {
        return mdsCuringTimeService.sync(mesCuringTimes);
    }

    @Override
    public BaseResponse<Void> handleFinishedProductDelivery(String scenario, List<MesFinishedProductDelivery> mesFinishedProductDeliveries) {
        return mdsFinishedProductDeliveryService.sync(mesFinishedProductDeliveries);
    }

    @Override
    public List<NewProductStockPointVO> getYpFactoryCode(String scenario) {
        return newProductStockPointService.getYpFactoryCode();
    }

    @Override
    public Map<String, List<String>> selectFg2SaMap(String scenario, List<String> fgCodes) {
        return mdsProductStockPointBaseService.selectFg2SaList(fgCodes);
    }

    @Override
    public Map<String, NewProductStockPointVO> selectMaxEopMinSop(Map<String, Object> params) {
        return newProductStockPointService.selectMaxEopMinSop(params);
    }

    @Override
    public List<MdsProductStockPointBaseVO> selectMdsProductStockPointBaseInfo(@RequestBody List<String> productCodes) {
        return mdsProductStockPointBaseService.selectByProductCodes(productCodes);
    }

    @Override
    public Map<String, String> selectMainProcessByProductCodes(@RequestBody List<String> productCodes) {
        return newRoutingStepResourceService.selectMainProcessByProductCodes(productCodes);
    }

    @Override
    public List<String> selectDistinctProductCodesByParams(Map<String, Object> params) {
        return newProductStockPointService.selectDistinctProductCodesByParams(params);
    }

    @Override
    public List<PhysicalResourceVO> selectVOByPhysicalIds(List<String> resourceIds) {
        return physicalResourceDao.selectVOByParams(ImmutableMap.of("ids", resourceIds));
    }


	@Override
	public void updateOrderPlanner(String scenario, List<String> productCodes,
			String orderPlanner) {
		newProductStockPointService.updateOrderPlanner(productCodes, orderPlanner);
	}

    @Override
    public List<LabelValue<String>> selectMoldToolingGroupByParams(String scenario, String organizationId, String standardResourceCode) {
        List<LabelValue<String>> result = Lists.newArrayList();
        if (StringUtils.isBlank(organizationId)) {
            return result;
        }
        Map<String, Object> queryParam = MapUtil.newHashMap();
        queryParam.put("organizationId", organizationId);
        queryParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<MoldToolingGroupVO> moldToolingGroupVOS = moldToolingGroupService.selectByParams(queryParam);
        if (CollectionUtils.isEmpty(moldToolingGroupVOS)) {
            return result;
        }
        for (MoldToolingGroupVO moldToolingGroupVO : moldToolingGroupVOS) {
            if (StringUtils.isNotBlank(standardResourceCode) && !moldToolingGroupVO.getStandardResourceCode().contains(standardResourceCode)) {
                continue;
            }
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setLabel(moldToolingGroupVO.getStandardResourceCode());
            labelValue.setValue(moldToolingGroupVO.getId());
            result.add(labelValue);
        }
        return result;
    }

    @Override
    public List<LabelValue<String>> selectMoldToolingByStandResourceId(String scenario, String standardResourceId) {
        List<LabelValue<String>> result = Lists.newArrayList();
        if (StringUtils.isBlank(standardResourceId)) {
            return result;
        }
        Map<String, Object> queryParam = MapUtil.newHashMap();
        queryParam.put("standardResourceId", standardResourceId);
        queryParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<MoldToolingVO> moldToolingVOS = moldToolingService.selectByParams(queryParam);
        if (CollectionUtils.isEmpty(moldToolingVOS)) {
            return result;
        }
        for (MoldToolingVO moldToolingVO : moldToolingVOS) {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setLabel(moldToolingVO.getPhysicalResourceCode());
            labelValue.setValue(moldToolingVO.getId());
            result.add(labelValue);
        }
        return result;
    }
}
